//
// Created by faz<PERSON><PERSON> on 2024/3/17.
//

#include "vofa.h"

static uint8_t vofa_buffer[VOFA_FLOAT_NUM*4+4];
static uint16_t cnt = 0;

void vofa_add_data(float data)
{
    vofa_buffer[cnt ++] = *((uint8_t *)(&data));
    vofa_buffer[cnt ++] = *((uint8_t *)(&data)+1);
    vofa_buffer[cnt ++] = *((uint8_t *)(&data)+2);
    vofa_buffer[cnt ++] = *((uint8_t *)(&data)+3);
}

void vofa_send()
{
    vofa_buffer[cnt ++] = 0x00;
    vofa_buffer[cnt ++] = 0x00;
    vofa_buffer[cnt ++] = 0x80;
    vofa_buffer[cnt ++] = 0x7f;

//    debug_send_buffer(vofa_buffer, cnt);
    nrf24l01_send_data(vofa_buffer, cnt);
    cnt = 0;
}
