//
// Created by f<PERSON><PERSON><PERSON> on 2024/7/26.
//

#include "protocol.h"

uint16_t adcValue1, adcValue2;
uint16_t medAdcValue1, medAdcValue2;

void protocol_analysis()
{
    // if (receive_buffer[0] == 'C'){
    //     adcValue1 = (receive_buffer[3] << 8) | (receive_buffer[4]);
    //     medAdcValue1 = (receive_buffer[5]<<8)|(receive_buffer[6]);
    //     adcValue2 = (receive_buffer[7] << 8) | (receive_buffer[8]);
    //     medAdcValue2 = (receive_buffer[9]<<8)|(receive_buffer[10]);
    // }
}
