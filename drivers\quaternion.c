#include "quaternion.h"
#include "imu660rb.h"
#include "delay.h"
#include "tft180.h"
#include <math.h>

// 误差积分
float I_ex, I_ey, I_ez;

// 四元数初始化
float Q_info_q0 = 1.0, Q_info_q1 = 0.0, Q_info_q2 = 0.0, Q_info_q3 = 0.0;

// 欧拉角输出
float eulerAngle_pitch = 0.0, eulerAngle_roll = 0.0, eulerAngle_yaw = 0.0;

// PI控制器参数
float icm_kp = 0.5;     // 加速度计的收敛速率比例增益
float icm_ki = 0.0025;  // 陀螺仪收敛速率的积分增益

// 陀螺仪偏移量
float gx_offset = 0.0, gy_offset = 0.0, gz_offset = 0.0;

// 滤波后的传感器数据
float Angle_gx = 0.0, Angle_gy = 0.0, Angle_gz = 0.0;
float Angle_ax = 0.0, Angle_ay = 0.0, Angle_az = 0.0;

void gyroOffsetInit(void)
{
    gx_offset = 0;
    gy_offset = 0;
    gz_offset = 0;

    delay_ms(500);  // 等待传感器稳定

    for (int i = 0; i < 1500; ++i)
    {
        Read_IMU660RB();  // 读取IMU数据
        gx_offset += angular_rate_mdps[0];  // X轴角速度
        gy_offset += angular_rate_mdps[1];  // Y轴角速度
        gz_offset += angular_rate_mdps[2];  // Z轴角速度

        delay_ms(2);
    }

    gx_offset *= 0.001;  // 计算平均值
    gy_offset *= 0.001;
    gz_offset *= 0.001;
}

// 快速计算 1/平方根
float invSqrt(float x)
{
    float halfx = 0.5f * x;
    float y = x;
    long i = *(long*)&y;
    i = 0x5f3759df - (i >> 1);
    y = *(float*)&i;
    y = y * (1.5f - (halfx * y * y));
    return y;
}

void icmGetValues(void)
{
    float alpha = 0.3;  // 低通滤波系数

    // 一阶低通滤波，将mg转换为g (1g = 1000mg)
    Angle_ax = (acceleration_mg[0] * alpha) * 0.001 + Angle_ax * (1 - alpha);
    Angle_ay = (acceleration_mg[1] * alpha) * 0.001 + Angle_ay * (1 - alpha);
    Angle_az = (acceleration_mg[2] * alpha) * 0.001 + Angle_az * (1 - alpha);

    // 陀螺仪角速度转换为弧度制: mdps -> rad/s
    // 1 mdps = 0.001 dps, 1 dps = π/180 rad/s
    // 所以 1 mdps = 0.001 * π/180 = 0.0000174533 rad/s
    Angle_gx = (angular_rate_mdps[0] - gx_offset) * 0.0000174533;
    Angle_gy = (angular_rate_mdps[1] - gy_offset) * 0.0000174533;
    Angle_gz = (angular_rate_mdps[2] - gz_offset) * 0.0000174533;
}

void icmAHRSupdate(void)
{
    float halfT = 0.5 * DELTA_T;  // 采样周期一半
    float vx, vy, vz;             // 当前姿态计算得来的重力在三轴上的分量
    float ex, ey, ez;             // 误差

    float q0 = Q_info_q0;  // 四元数
    float q1 = Q_info_q1;
    float q2 = Q_info_q2;
    float q3 = Q_info_q3;

    float q0q0 = q0 * q0;  // 预计算，提高效率
    float q0q1 = q0 * q1;
    float q0q2 = q0 * q2;
    float q1q1 = q1 * q1;
    float q1q3 = q1 * q3;
    float q2q2 = q2 * q2;
    float q2q3 = q2 * q3;
    float q3q3 = q3 * q3;

    // 加计处于自由落体状态时不进行姿态解算
    if(Angle_ax * Angle_ay * Angle_az == 0)
        return;

    // 对加速度数据进行归一化
    float norm = invSqrt(Angle_ax * Angle_ax + Angle_ay * Angle_ay + Angle_az * Angle_az);
    Angle_ax = Angle_ax * norm;
    Angle_ay = Angle_ay * norm;
    Angle_az = Angle_az * norm;

    // 载体坐标系下重力在三个轴上的分量
    vx = 2 * (q1q3 - q0q2);
    vy = 2 * (q0q1 + q2q3);
    vz = q0q0 - q1q1 - q2q2 + q3q3;

    // 向量叉乘，得到陀螺仪的校正补偿向量
    ex = Angle_ay * vz - Angle_az * vy;
    ey = Angle_az * vx - Angle_ax * vz;
    ez = Angle_ax * vy - Angle_ay * vx;

    // 误差累加
    I_ex += halfT * ex;
    I_ey += halfT * ey;
    I_ez += halfT * ez;

    // 使用PI控制器消除向量积误差
    Angle_gx = Angle_gx + icm_kp * ex + icm_ki * I_ex;
    Angle_gy = Angle_gy + icm_kp * ey + icm_ki * I_ey;
    Angle_gz = Angle_gz + icm_kp * ez + icm_ki * I_ez;

    // 一阶龙格库塔法求解四元数微分方程
    q0 = q0 + (-q1 * Angle_gx - q2 * Angle_gy - q3 * Angle_gz) * halfT;
    q1 = q1 + ( q0 * Angle_gx + q2 * Angle_gz - q3 * Angle_gy) * halfT;
    q2 = q2 + ( q0 * Angle_gy - q1 * Angle_gz + q3 * Angle_gx) * halfT;
    q3 = q3 + ( q0 * Angle_gz + q1 * Angle_gy - q2 * Angle_gx) * halfT;

    // 单位化四元数
    norm = invSqrt(q0 * q0 + q1 * q1 + q2 * q2 + q3 * q3);
    Q_info_q0 = q0 * norm;
    Q_info_q1 = q1 * norm;
    Q_info_q2 = q2 * norm;
    Q_info_q3 = q3 * norm;

    // 四元数转欧拉角 (弧度转角度: * 57.295779)
    eulerAngle_pitch = asin(2 * q0 * q2 - 2 * q1 * q3) * 57.295779;
    eulerAngle_roll = atan2(2 * q2 * q3 + 2 * q0 * q1, -2 * q1 * q1 - 2 * q2 * q2 + 1) * 57.295779;
    eulerAngle_yaw = atan2(2 * q1 * q2 + 2 * q0 * q3, -2 * q2 * q2 - 2 * q3 * q3 + 1) * 57.295779;
}

// 保持原函数名兼容性
void imu660rb_euler_show(void)
{
    // 添加标签，避免重叠
    static uint8_t first_display = 1;
    if(first_display) {
        tft180_show_string_color(5, 0, "Yaw:", BLACK, WHITE);
        tft180_show_string_color(5, 16, "Roll:", BLACK, WHITE);
        tft180_show_string_color(5, 32, "Pitch:", BLACK, WHITE);

        tft180_show_string_color(5, 48, "Gx:", BLACK, WHITE);
        tft180_show_string_color(5, 64, "Gy:", BLACK, WHITE);
        tft180_show_string_color(5, 80, "Gz:", BLACK, WHITE);

        tft180_show_string_color(5, 96, "Ax:", BLACK, WHITE);
        tft180_show_string_color(5, 112, "Ay:", BLACK, WHITE);
        tft180_show_string_color(80, 112, "Az:", BLACK, WHITE);
        first_display = 0;
    }

    // 显示欧拉角 - 使用固定宽度避免重叠
    tft180_show_num_color(40, 0, eulerAngle_yaw, 4, 1, BLACK, YELLOW);
    tft180_show_num_color(40, 16, eulerAngle_roll, 4, 1, BLACK, GREEN);
    tft180_show_num_color(40, 32, eulerAngle_pitch, 4, 1, BLACK, CYAN);

    // 显示滤波后的陀螺仪数据 (rad/s转换为dps显示)
    tft180_show_num_color(40, 48, Angle_gx * 57.295779f, 4, 1, BLACK, PURPLE);
    tft180_show_num_color(40, 64, Angle_gy * 57.295779f, 4, 1, BLACK, PURPLE);
    tft180_show_num_color(40, 80, Angle_gz * 57.295779f, 4, 1, BLACK, PURPLE);

    // 显示滤波后的加速度数据 (单位：g)
    tft180_show_num_color(40, 96, Angle_ax, 1, 2, BLACK, BROWN);
    tft180_show_num_color(40, 112, Angle_ay, 1, 2, BLACK, BROWN);
    tft180_show_num_color(110, 112, Angle_az, 1, 2, BLACK, BROWN);
}
