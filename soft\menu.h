//
// Created by faz<PERSON><PERSON> on 2023/12/26.
//

#ifndef MENU_H
#define MENU_H

#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

#include "common_inc.h"

#define MAX_PAGE_NUM            50

#define MAX_SHOW_NUM            5
#define FONT_WIDTH              6
#define FONT_HEIGHT             8

#define DISPLAY_OFFSET_X        0
#define DISPLAY_OFFSET_Y        0

#define ANIMATION_FLAG          (1)
#define ANIMATION_SPEED         (2)
#define ROUND_RECT_R            (FONT_HEIGHT/3)

#define NOP                 0
#define UP                  1
#define DOWN                2
#define ENTER               3
#define BACK                4
#define ONLY_SHOW           0x40
#define NO_SHOW             0x80
#define ENCODER_POS_PREFIX  0x20
#define ENCODER_NEG_PREFIX  0x10

typedef struct Page * xpPage;
typedef struct Page
{
    char * name;
    uint8_t (*func)(uint8_t);
    xpPage lastPage;
    xpPage nextPage;
    xpPage selectSubPage;
    xpPage subPageHead;
    xpPage subPageTail;
    xpPage parentPage;
}xPage;

extern uint8_t pageBufferPointer;

void menu_init();
void menu_task();

#endif //MENU_H
