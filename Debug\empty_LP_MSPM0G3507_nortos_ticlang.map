******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 22:01:47 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000021c1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00002f40  000050c0  R  X
  SRAM                  20200000   00004000  000003e8  00003c18  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002f40   00002f40    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000026c8   000026c8    r-x .text
  00002788    00002788    00000780   00000780    r-- .rodata
  00002f08    00002f08    00000038   00000038    r-- .cinit
20200000    20200000    000001eb   00000000    rw-
  20200000    20200000    000001d5   00000000    rw- .bss
  202001d8    202001d8    00000013   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000026c8     
                  000000c0    00000288     tft180.o (.text.tft180_init)
                  00000348    0000020c     encoder.o (.text.encoder_exti_callback)
                  00000554    00000204     openmv.o (.text.openmv_display_data)
                  00000758    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000944    000001bc     tft180.o (.text.func_float_to_str)
                  00000b00    00000130     tft180.o (.text.tft180_show_char_color)
                  00000c30    0000012c     openmv.o (.text.UART3_IRQHandler)
                  00000d5c    0000010c     tft180.o (.text.tft180_show_num_color)
                  00000e68    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000f6c    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001054    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000112c    000000d4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001200    00000098     empty.o (.text.main)
                  00001298    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  00001324    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  000013b0    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000143c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000014c0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000153c    00000078     openmv.o (.text.openmv_init)
                  000015b4    00000078     tft180.o (.text.tft180_clear_color)
                  0000162c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000016a0    00000074     delay.o (.text.delay_us)
                  00001714    0000006c     tft180.o (.text.tft180_set_region)
                  00001780    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000017e8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001850    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000018b2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000018b4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00001916    00000062     tft180.o (.text.tft180_show_string_color)
                  00001978    00000058     openmv.o (.text.openmv_reset_uart_state)
                  000019d0    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00001a26    00000002     empty.o (.text.timerA_callback)
                  00001a28    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00001a7c    00000050     openmv.o (.text.openmv_is_data_valid)
                  00001acc    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001b18    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001b60    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00001ba8    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  00001bf0    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00001c34    00000044     tft180.o (.text.tft180_write_16bit_data)
                  00001c78    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_IMU660RB_init)
                  00001cb8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFT_SPI_init)
                  00001cf8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  00001d38    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001d78    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001db4    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  00001df0    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001e2c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00001e68    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001ea4    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001ede    00000002     empty.o (.text.timerB_callback)
                  00001ee0    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00001f1a    00000002     --HOLE-- [fill = 0]
                  00001f1c    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00001f54    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001f88    00000034     openmv.o (.text.openmv_analysis)
                  00001fbc    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00001fec    00000030     tft180.o (.text.tft180_write_index)
                  0000201c    0000002c     openmv.o (.text.__NVIC_ClearPendingIRQ)
                  00002048    0000002c     timer.o (.text.__NVIC_ClearPendingIRQ)
                  00002074    0000002c     openmv.o (.text.__NVIC_EnableIRQ)
                  000020a0    0000002c     timer.o (.text.__NVIC_EnableIRQ)
                  000020cc    0000002c     tft180.o (.text.tft180_write_8bit_data)
                  000020f8    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002120    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  00002148    00000028     timer.o (.text.TIMG8_IRQHandler)
                  00002170    00000028     debug.o (.text.UART0_IRQHandler)
                  00002198    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000021c0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000021e8    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  0000220c    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000222e    00000002     --HOLE-- [fill = 0]
                  00002230    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002250    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000226e    00000002     --HOLE-- [fill = 0]
                  00002270    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  0000228c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000022a8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000022c4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000022e0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000022fc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002318    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002334    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002350    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000236c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002388    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  000023a4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000023bc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000023d4    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000023ec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002404    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000241c    00000018     tft180.o (.text.DL_GPIO_setPins)
                  00002434    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  0000244c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002464    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  0000247c    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  00002494    00000018     tft180.o (.text.DL_SPI_isBusy)
                  000024ac    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  000024c4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000024dc    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000024f4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0000250c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002524    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000253c    00000018     openmv.o (.text.DL_UART_isRXFIFOEmpty)
                  00002554    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0000256c    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002582    00000016     tft180.o (.text.DL_SPI_transmitData8)
                  00002598    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000025ae    00000016     delay.o (.text.delay_ms)
                  000025c4    00000016     timer.o (.text.timerA_init)
                  000025da    00000016     timer.o (.text.timerB_init)
                  000025f0    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002606    00000014     tft180.o (.text.DL_GPIO_clearPins)
                  0000261a    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000262e    00000002     --HOLE-- [fill = 0]
                  00002630    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00002644    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002658    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000266c    00000014     debug.o (.text.DL_UART_receiveData)
                  00002680    00000014     openmv.o (.text.DL_UART_receiveData)
                  00002694    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  000026a6    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  000026b8    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  000026ca    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  000026dc    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000026ee    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00002700    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00002712    00000002     --HOLE-- [fill = 0]
                  00002714    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00002724    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002734    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002744    0000000c     timer.o (.text.get_system_time_ms)
                  00002750    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000275a    00000008     empty.o (.text.GROUP1_IRQHandler)
                  00002762    00000002     --HOLE-- [fill = 0]
                  00002764    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000276c    00000006     libc.a : exit.c.obj (.text:abort)
                  00002772    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002776    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000277a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000277e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002782    00000006     --HOLE-- [fill = 0]

.cinit     0    00002f08    00000038     
                  00002f08    0000000f     (.cinit..data.load) [load image, compression = lzss]
                  00002f17    00000001     --HOLE-- [fill = 0]
                  00002f18    0000000c     (__TI_handler_table)
                  00002f24    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002f2c    00000010     (__TI_cinit_table)
                  00002f3c    00000004     --HOLE-- [fill = 0]

.rodata    0    00002788    00000780     
                  00002788    000005f0     tft180.o (.rodata.ascii_font_8x16)
                  00002d78    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  00002d8c    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  00002da0    00000014     empty.o (.rodata.str1.5792233746214848027.1)
                  00002db4    00000013     openmv.o (.rodata.str1.14055760531511630791.1)
                  00002dc7    00000012     openmv.o (.rodata.str1.10222307361326560281.1)
                  00002dd9    00000012     openmv.o (.rodata.str1.10322375466862398049.1)
                  00002deb    00000012     openmv.o (.rodata.str1.10499335994202400488.1)
                  00002dfd    00000012     openmv.o (.rodata.str1.11972700756869316087.1)
                  00002e0f    00000012     openmv.o (.rodata.str1.12960560650680968270.1)
                  00002e21    00000012     openmv.o (.rodata.str1.12983843792890534433.1)
                  00002e33    00000012     openmv.o (.rodata.str1.16410698957387474858.1)
                  00002e45    00000012     openmv.o (.rodata.str1.4018680016288177859.1)
                  00002e57    00000012     openmv.o (.rodata.str1.5573925365973559781.1)
                  00002e69    00000012     openmv.o (.rodata.str1.8871796710599046883.1)
                  00002e7b    00000012     openmv.o (.rodata.str1.9558640640855864504.1)
                  00002e8d    0000000b     empty.o (.rodata.str1.4000995719088696555.1)
                  00002e98    0000000a     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_config)
                  00002ea2    0000000a     ti_msp_dl_config.o (.rodata.gTFT_SPI_config)
                  00002eac    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00002eb6    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00002ec0    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  00002eca    0000000a     openmv.o (.rodata.str1.16395811435273266920.1)
                  00002ed4    0000000a     openmv.o (.rodata.str1.9416414711272993270.1)
                  00002ede    00000002     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_clockConfig)
                  00002ee0    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  00002ee8    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  00002ef0    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  00002ef3    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  00002ef6    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  00002ef9    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  00002efc    00000003     openmv.o (.rodata.str1.15289475315984735280.1)
                  00002eff    00000002     ti_msp_dl_config.o (.rodata.gTFT_SPI_clockConfig)
                  00002f01    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00002f03    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00002f05    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  00002f07    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001d5     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    00000028     (.common:gSPI_IMU660RBBackup)
                  20200198    00000028     (.common:gTFT_SPIBackup)
                  202001c0    0000000c     (.common:openmvData)
                  202001cc    00000008     openmv.o (.bss.rx_buffer)
                  202001d4    00000001     openmv.o (.bss.data)

.data      0    202001d8    00000013     UNINITIALIZED
                  202001d8    00000004     timer.o (.data.system_time_ms)
                  202001dc    00000002     encoder.o (.data.left_counter)
                  202001de    00000002     encoder.o (.data.right_counter)
                  202001e0    00000002     openmv.o (.data.tft180_bgcolor)
                  202001e2    00000002     tft180.o (.data.tft180_bgcolor)
                  202001e4    00000002     openmv.o (.data.tft180_pencolor)
                  202001e6    00000001     openmv.o (.data.n)
                  202001e7    00000001     openmv.o (.data.state)
                  202001e8    00000001     tft180.o (.data.tft180_x_max)
                  202001e9    00000001     tft180.o (.data.tft180_y_max)
                  202001ea    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2814   128       448    
       startup_mspm0g350x_ticlang.o   8      192       0      
       empty.o                        164    31        0      
    +--+------------------------------+------+---------+---------+
       Total:                         2986   351       448    
                                                              
    .\drivers\
       tft180.o                       2240   1520      4      
       openmv.o                       1306   240       27     
       encoder.o                      598    0         4      
       timer.o                        230    0         4      
    +--+------------------------------+------+---------+---------+
       Total:                         4374   1760      39     
                                                              
    .\soft\
       delay.o                        138    0         0      
       debug.o                        78     0         1      
    +--+------------------------------+------+---------+---------+
       Total:                         216    0         1      
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588    0         0      
       dl_uart.o                      90     0         0      
       dl_spi.o                       86     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         774    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparedf2.c.obj               220    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_dcmp.S.obj               98     0         0      
       aeabi_fcmp.S.obj               98     0         0      
       aeabi_idivmod.S.obj            86     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1264   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      51        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   9910   2162      1000   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002f2c records: 2, size/record: 8, table size: 16
	.data: load addr=00002f08, load size=0000000f bytes, run addr=202001d8, run size=00000013 bytes, compression=lzss
	.bss: load addr=00002f24, load size=00000008 bytes, run addr=20200000, run size=000001d5 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002f18 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00002773  ADC0_IRQHandler                 
00002773  ADC1_IRQHandler                 
00002773  AES_IRQHandler                  
00002776  C$$EXIT                         
00002773  CANFD0_IRQHandler               
00002773  DAC0_IRQHandler                 
00002751  DL_Common_delayCycles           
00001bf1  DL_SPI_init                     
00002695  DL_SPI_setClockConfig           
00000e69  DL_Timer_initFourCCPWMMode      
00000f6d  DL_Timer_initTimerMode          
00002335  DL_Timer_setCaptCompUpdateMethod
0000250d  DL_Timer_setCaptureCompareOutCtl
00002725  DL_Timer_setCaptureCompareValue 
00002351  DL_Timer_setClockConfig         
00001b19  DL_UART_init                    
000026dd  DL_UART_setClockConfig          
00002773  DMA_IRQHandler                  
00002773  Default_Handler                 
00002773  GROUP0_IRQHandler               
0000275b  GROUP1_IRQHandler               
00002777  HOSTexit                        
00002773  HardFault_Handler               
00002773  I2C0_IRQHandler                 
00002773  I2C1_IRQHandler                 
00002773  NMI_Handler                     
00002773  PendSV_Handler                  
00002773  RTC_IRQHandler                  
0000277b  Reset_Handler                   
00002773  SPI0_IRQHandler                 
00002773  SPI1_IRQHandler                 
00002773  SVC_Handler                     
00000759  SYSCFG_DL_GPIO_init             
00001299  SYSCFG_DL_PWM_6_init            
00001325  SYSCFG_DL_PWM_7_init            
00001c79  SYSCFG_DL_SPI_IMU660RB_init     
0000220d  SYSCFG_DL_SYSCTL_init           
00002735  SYSCFG_DL_SYSTICK_init          
00001cb9  SYSCFG_DL_TFT_SPI_init          
00001db5  SYSCFG_DL_TIMER_12_init         
00001cf9  SYSCFG_DL_TIMER_8_init          
00001b61  SYSCFG_DL_UART_0_init           
00001a29  SYSCFG_DL_UART_1_init           
00001ba9  SYSCFG_DL_UART_3_init           
00001781  SYSCFG_DL_init                  
0000112d  SYSCFG_DL_initPower             
00002773  SysTick_Handler                 
00002773  TIMA0_IRQHandler                
00002773  TIMA1_IRQHandler                
00002773  TIMG0_IRQHandler                
00002389  TIMG12_IRQHandler               
00002773  TIMG6_IRQHandler                
00002773  TIMG7_IRQHandler                
00002149  TIMG8_IRQHandler                
000026ef  TI_memcpy_small                 
00002171  UART0_IRQHandler                
00002773  UART1_IRQHandler                
00002773  UART2_IRQHandler                
00000c31  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00002f2c  __TI_CINIT_Base                 
00002f3c  __TI_CINIT_Limit                
00002f3c  __TI_CINIT_Warm                 
00002f18  __TI_Handler_Table_Base         
00002f24  __TI_Handler_Table_Limit        
00001e69  __TI_auto_init_nobinit_nopinit  
000014c1  __TI_decompress_lzss            
00002701  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
000025f1  __TI_zero_init_nomemset         
0000105f  __addsf3                        
00001851  __aeabi_dcmpeq                  
0000188d  __aeabi_dcmpge                  
000018a1  __aeabi_dcmpgt                  
00001879  __aeabi_dcmple                  
00001865  __aeabi_dcmplt                  
00001d39  __aeabi_f2d                     
00001f1d  __aeabi_f2iz                    
0000105f  __aeabi_fadd                    
000018b5  __aeabi_fcmpeq                  
000018f1  __aeabi_fcmpge                  
00001905  __aeabi_fcmpgt                  
000018dd  __aeabi_fcmple                  
000018c9  __aeabi_fcmplt                  
000013b1  __aeabi_fmul                    
00001055  __aeabi_fsub                    
00001df1  __aeabi_i2f                     
000019d1  __aeabi_idiv                    
000018b3  __aeabi_idiv0                   
000019d1  __aeabi_idivmod                 
00002765  __aeabi_memcpy                  
00002765  __aeabi_memcpy4                 
00002765  __aeabi_memcpy8                 
00002199  __aeabi_ui2f                    
ffffffff  __binit__                       
000017e9  __cmpdf2                        
00001ea5  __cmpsf2                        
000017e9  __eqdf2                         
00001ea5  __eqsf2                         
00001d39  __extendsfdf2                   
00001f1d  __fixsfsi                       
00001df1  __floatsisf                     
00002199  __floatunsisf                   
0000162d  __gedf2                         
00001e2d  __gesf2                         
0000162d  __gtdf2                         
00001e2d  __gtsf2                         
000017e9  __ledf2                         
00001ea5  __lesf2                         
000017e9  __ltdf2                         
00001ea5  __ltsf2                         
UNDEFED   __mpu_init                      
00001ee1  __muldsi3                       
000013b1  __mulsf3                        
000017e9  __nedf2                         
00001ea5  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00001055  __subsf3                        
000021c1  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
0000277f  _system_pre_init                
0000276d  abort                           
00002788  ascii_font_8x16                 
ffffffff  binit                           
000025af  delay_ms                        
000016a1  delay_us                        
00000349  encoder_exti_callback           
00000945  func_float_to_str               
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
20200140  gUART_3Backup                   
00002745  get_system_time_ms              
00000000  interruptVectors                
202001dc  left_counter                    
00001201  main                            
202001c0  openmvData                      
00001f89  openmv_analysis                 
00000555  openmv_display_data             
0000153d  openmv_init                     
00001a7d  openmv_is_data_valid            
00001979  openmv_reset_uart_state         
202001de  right_counter                   
000015b5  tft180_clear_color              
000000c1  tft180_init                     
00000b01  tft180_show_char_color          
00000d5d  tft180_show_num_color           
00001917  tft180_show_string_color        
00001c35  tft180_write_16bit_data         
000020cd  tft180_write_8bit_data          
00001a27  timerA_callback                 
000025c5  timerA_init                     
00001edf  timerB_callback                 
000025db  timerB_init                     
202001ea  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  tft180_init                     
00000200  __STACK_SIZE                    
00000349  encoder_exti_callback           
00000555  openmv_display_data             
00000759  SYSCFG_DL_GPIO_init             
00000945  func_float_to_str               
00000b01  tft180_show_char_color          
00000c31  UART3_IRQHandler                
00000d5d  tft180_show_num_color           
00000e69  DL_Timer_initFourCCPWMMode      
00000f6d  DL_Timer_initTimerMode          
00001055  __aeabi_fsub                    
00001055  __subsf3                        
0000105f  __addsf3                        
0000105f  __aeabi_fadd                    
0000112d  SYSCFG_DL_initPower             
00001201  main                            
00001299  SYSCFG_DL_PWM_6_init            
00001325  SYSCFG_DL_PWM_7_init            
000013b1  __aeabi_fmul                    
000013b1  __mulsf3                        
000014c1  __TI_decompress_lzss            
0000153d  openmv_init                     
000015b5  tft180_clear_color              
0000162d  __gedf2                         
0000162d  __gtdf2                         
000016a1  delay_us                        
00001781  SYSCFG_DL_init                  
000017e9  __cmpdf2                        
000017e9  __eqdf2                         
000017e9  __ledf2                         
000017e9  __ltdf2                         
000017e9  __nedf2                         
00001851  __aeabi_dcmpeq                  
00001865  __aeabi_dcmplt                  
00001879  __aeabi_dcmple                  
0000188d  __aeabi_dcmpge                  
000018a1  __aeabi_dcmpgt                  
000018b3  __aeabi_idiv0                   
000018b5  __aeabi_fcmpeq                  
000018c9  __aeabi_fcmplt                  
000018dd  __aeabi_fcmple                  
000018f1  __aeabi_fcmpge                  
00001905  __aeabi_fcmpgt                  
00001917  tft180_show_string_color        
00001979  openmv_reset_uart_state         
000019d1  __aeabi_idiv                    
000019d1  __aeabi_idivmod                 
00001a27  timerA_callback                 
00001a29  SYSCFG_DL_UART_1_init           
00001a7d  openmv_is_data_valid            
00001b19  DL_UART_init                    
00001b61  SYSCFG_DL_UART_0_init           
00001ba9  SYSCFG_DL_UART_3_init           
00001bf1  DL_SPI_init                     
00001c35  tft180_write_16bit_data         
00001c79  SYSCFG_DL_SPI_IMU660RB_init     
00001cb9  SYSCFG_DL_TFT_SPI_init          
00001cf9  SYSCFG_DL_TIMER_8_init          
00001d39  __aeabi_f2d                     
00001d39  __extendsfdf2                   
00001db5  SYSCFG_DL_TIMER_12_init         
00001df1  __aeabi_i2f                     
00001df1  __floatsisf                     
00001e2d  __gesf2                         
00001e2d  __gtsf2                         
00001e69  __TI_auto_init_nobinit_nopinit  
00001ea5  __cmpsf2                        
00001ea5  __eqsf2                         
00001ea5  __lesf2                         
00001ea5  __ltsf2                         
00001ea5  __nesf2                         
00001edf  timerB_callback                 
00001ee1  __muldsi3                       
00001f1d  __aeabi_f2iz                    
00001f1d  __fixsfsi                       
00001f89  openmv_analysis                 
000020cd  tft180_write_8bit_data          
00002149  TIMG8_IRQHandler                
00002171  UART0_IRQHandler                
00002199  __aeabi_ui2f                    
00002199  __floatunsisf                   
000021c1  _c_int00_noargs                 
0000220d  SYSCFG_DL_SYSCTL_init           
00002335  DL_Timer_setCaptCompUpdateMethod
00002351  DL_Timer_setClockConfig         
00002389  TIMG12_IRQHandler               
0000250d  DL_Timer_setCaptureCompareOutCtl
000025af  delay_ms                        
000025c5  timerA_init                     
000025db  timerB_init                     
000025f1  __TI_zero_init_nomemset         
00002695  DL_SPI_setClockConfig           
000026dd  DL_UART_setClockConfig          
000026ef  TI_memcpy_small                 
00002701  __TI_decompress_none            
00002725  DL_Timer_setCaptureCompareValue 
00002735  SYSCFG_DL_SYSTICK_init          
00002745  get_system_time_ms              
00002751  DL_Common_delayCycles           
0000275b  GROUP1_IRQHandler               
00002765  __aeabi_memcpy                  
00002765  __aeabi_memcpy4                 
00002765  __aeabi_memcpy8                 
0000276d  abort                           
00002773  ADC0_IRQHandler                 
00002773  ADC1_IRQHandler                 
00002773  AES_IRQHandler                  
00002773  CANFD0_IRQHandler               
00002773  DAC0_IRQHandler                 
00002773  DMA_IRQHandler                  
00002773  Default_Handler                 
00002773  GROUP0_IRQHandler               
00002773  HardFault_Handler               
00002773  I2C0_IRQHandler                 
00002773  I2C1_IRQHandler                 
00002773  NMI_Handler                     
00002773  PendSV_Handler                  
00002773  RTC_IRQHandler                  
00002773  SPI0_IRQHandler                 
00002773  SPI1_IRQHandler                 
00002773  SVC_Handler                     
00002773  SysTick_Handler                 
00002773  TIMA0_IRQHandler                
00002773  TIMA1_IRQHandler                
00002773  TIMG0_IRQHandler                
00002773  TIMG6_IRQHandler                
00002773  TIMG7_IRQHandler                
00002773  UART1_IRQHandler                
00002773  UART2_IRQHandler                
00002776  C$$EXIT                         
00002777  HOSTexit                        
0000277b  Reset_Handler                   
0000277f  _system_pre_init                
00002788  ascii_font_8x16                 
00002f18  __TI_Handler_Table_Base         
00002f24  __TI_Handler_Table_Limit        
00002f2c  __TI_CINIT_Base                 
00002f3c  __TI_CINIT_Limit                
00002f3c  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
202001c0  openmvData                      
202001dc  left_counter                    
202001de  right_counter                   
202001ea  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[173 symbols]
