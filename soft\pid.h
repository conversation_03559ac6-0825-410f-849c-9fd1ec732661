//
// Created by faz<PERSON><PERSON> on 2024/3/26.
//

#ifndef PID_H
#define PID_H

typedef struct {
    float P;
    float I;
    float D;

    float out_threshold;

    float p_out;
    float i_out;
    float d_out;
    float out;

    float current_error;
    float last_error;
    float before_last_error;
}incremental_pid_t;

typedef struct {
    float P;
    float I;
    float D;

    float out_threshold;
    float integral_threshold;

    float last_error;
    float integral_error;
    float differential_error;
}position_pid_t;

void incremental_pid_init(incremental_pid_t *pid, float p, float i, float d, float out_threshold);
float incremental_pid(incremental_pid_t * pid, float current_value, float target_value);
void position_pid_init(position_pid_t * pid, float p, float i, float d, float out_threshold, float integral_threshold);
float position_pid(position_pid_t * pid, float current_value, float target_value);

#endif //PID_H
