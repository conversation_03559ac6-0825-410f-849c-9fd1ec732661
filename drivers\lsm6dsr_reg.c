/*
 * LSM6DSR Register Operations Implementation
 */

#include "lsm6dsr_reg.h"

/**
  * @defgroup  LSM6DSR_Private_Functions
  * @brief     This section groups all the functions concerning device
  * @{
  *
  */

static int32_t read_reg(stmdev_ctx_t *ctx, uint8_t reg, uint8_t *data, uint16_t len);
static int32_t write_reg(stmdev_ctx_t *ctx, uint8_t reg, uint8_t *data, uint16_t len);

/**
  * @brief  Read generic device register
  *
  * @param  ctx   read / write interface definitions(ptr)
  * @param  reg   register to read
  * @param  data  pointer to buffer that store the data read(ptr)
  * @param  len   number of consecutive register to read
  * @retval       interface status (MANDATORY: return 0 -> no Error)
  *
  */
static int32_t read_reg(stmdev_ctx_t *ctx, uint8_t reg, uint8_t *data, uint16_t len)
{
  int32_t ret;
  ret = ctx->read_reg(ctx->handle, reg, data, len);
  return ret;
}

/**
  * @brief  Write generic device register
  *
  * @param  ctx   read / write interface definitions(ptr)
  * @param  reg   register to write
  * @param  data  pointer to data to write in register reg(ptr)
  * @param  len   number of consecutive register to write
  * @retval       interface status (MANDATORY: return 0 -> no Error)
  *
  */
static int32_t write_reg(stmdev_ctx_t *ctx, uint8_t reg, uint8_t *data, uint16_t len)
{
  int32_t ret;
  ret = ctx->write_reg(ctx->handle, reg, data, len);
  return ret;
}

/**
  * @brief  Device Who amI.[get]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  buff   Buffer that stores data read
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_device_id_get(stmdev_ctx_t *ctx, uint8_t *buff)
{
  int32_t ret;
  ret = read_reg(ctx, LSM6DSR_WHO_AM_I, buff, 1);
  return ret;
}

/**
  * @brief  Software reset. Restore the default values in user registers[set]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Change the values of sw_reset in reg CTRL3_C
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_reset_set(stmdev_ctx_t *ctx, uint8_t val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_CTRL3_C, &reg, 1);
  if (ret == 0)
  {
    reg = (reg & ~0x01U) | ((uint8_t)val & 0x01U);
    ret = write_reg(ctx, LSM6DSR_CTRL3_C, &reg, 1);
  }
  return ret;
}

/**
  * @brief  Software reset. Restore the default values in user registers[get]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Change the values of sw_reset in reg CTRL3_C
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_reset_get(stmdev_ctx_t *ctx, uint8_t *val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_CTRL3_C, &reg, 1);
  *val = reg & 0x01U;

  return ret;
}

/**
  * @brief  Disable / Enable I3C interface.[set]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Change the values of i3c_disable in reg CTRL9_XL
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_i3c_disable_set(stmdev_ctx_t *ctx, lsm6dsr_i3c_disable_t val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_CTRL9_XL, &reg, 1);
  if (ret == 0)
  {
    reg = (reg & ~0x06U) | (((uint8_t)val << 1) & 0x06U);
    ret = write_reg(ctx, LSM6DSR_CTRL9_XL, &reg, 1);
  }
  return ret;
}

/**
  * @brief  Block Data Update.[set]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Change the values of bdu in reg CTRL3_C
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_block_data_update_set(stmdev_ctx_t *ctx, uint8_t val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_CTRL3_C, &reg, 1);
  if (ret == 0)
  {
    reg = (reg & ~0x40U) | ((uint8_t)val << 6);
    ret = write_reg(ctx, LSM6DSR_CTRL3_C, &reg, 1);
  }
  return ret;
}

/**
  * @brief  Accelerometer data rate selection.[set]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Change the values of odr_xl in reg CTRL1_XL
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_xl_data_rate_set(stmdev_ctx_t *ctx, lsm6dsr_odr_xl_t val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_CTRL1_XL, &reg, 1);
  if (ret == 0)
  {
    reg = (reg & ~0xF0U) | ((uint8_t)val << 4);
    ret = write_reg(ctx, LSM6DSR_CTRL1_XL, &reg, 1);
  }
  return ret;
}

/**
  * @brief  Gyroscope data rate selection.[set]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Change the values of odr_g in reg CTRL2_G
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_gy_data_rate_set(stmdev_ctx_t *ctx, lsm6dsr_odr_g_t val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_CTRL2_G, &reg, 1);
  if (ret == 0)
  {
    reg = (reg & ~0xF0U) | ((uint8_t)val << 4);
    ret = write_reg(ctx, LSM6DSR_CTRL2_G, &reg, 1);
  }
  return ret;
}

/**
  * @brief  Accelerometer full-scale selection.[set]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Change the values of fs_xl in reg CTRL1_XL
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_xl_full_scale_set(stmdev_ctx_t *ctx, lsm6dsr_fs_xl_t val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_CTRL1_XL, &reg, 1);
  if (ret == 0)
  {
    reg = (reg & ~0x0CU) | ((uint8_t)val << 2);
    ret = write_reg(ctx, LSM6DSR_CTRL1_XL, &reg, 1);
  }
  return ret;
}

/**
  * @brief  Gyroscope full-scale selection.[set]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Change the values of fs_g in reg CTRL2_G
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_gy_full_scale_set(stmdev_ctx_t *ctx, lsm6dsr_fs_g_t val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_CTRL2_G, &reg, 1);
  if (ret == 0)
  {
    reg = (reg & ~0x0CU) | ((uint8_t)val << 2);
    ret = write_reg(ctx, LSM6DSR_CTRL2_G, &reg, 1);
  }
  return ret;
}

/**
  * @brief  Gyroscope low pass filter 1 bandwidth selection.[set]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Change the values of ftype in reg CTRL6_C
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_gy_filter_lp1_set(stmdev_ctx_t *ctx, uint8_t val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_CTRL6_C, &reg, 1);
  if (ret == 0)
  {
    reg = (reg & ~0x03U) | (val & 0x03U);
    ret = write_reg(ctx, LSM6DSR_CTRL6_C, &reg, 1);
  }
  return ret;
}

/**
  * @brief  Select the signal that need to route on int1 pad.[get]
  *
  * @param  ctx      Read / write interface definitions.(ptr)
  * @param  val      Get the values of reg INT1_CTRL
  * @retval          Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_pin_int1_route_get(stmdev_ctx_t *ctx, lsm6dsr_pin_int1_route_t *val)
{
  int32_t ret;
  ret = read_reg(ctx, LSM6DSR_INT1_CTRL, (uint8_t*)&(val->int1_ctrl), 1);
  return ret;
}

/**
  * @brief  Select the signal that need to route on int1 pad.[set]
  *
  * @param  ctx      Read / write interface definitions.(ptr)
  * @param  val      Change the values of reg INT1_CTRL
  * @retval          Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_pin_int1_route_set(stmdev_ctx_t *ctx, lsm6dsr_pin_int1_route_t *val)
{
  int32_t ret;
  ret = write_reg(ctx, LSM6DSR_INT1_CTRL, (uint8_t*)&(val->int1_ctrl), 1);
  return ret;
}

/**
  * @brief  Data-ready pulsed / letched mode.[set]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Change the values of dataready_pulsed in reg COUNTER_BDR_REG1
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_data_ready_mode_set(stmdev_ctx_t *ctx, lsm6dsr_dataready_pulsed_t val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_COUNTER_BDR_REG1, &reg, 1);
  if (ret == 0)
  {
    reg = (reg & ~0x80U) | ((uint8_t)val << 7);
    ret = write_reg(ctx, LSM6DSR_COUNTER_BDR_REG1, &reg, 1);
  }
  return ret;
}

/**
  * @brief  Get the status of all the interrupt sources.[get]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  val    Get the values of reg STATUS_REG
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_gy_flag_data_ready_get(stmdev_ctx_t *ctx, uint8_t *val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_STATUS_REG, &reg, 1);
  *val = (reg & 0x02U) >> 1;

  return ret;
}

/**
  * @brief  Angular rate sensor. The value is expressed as a 16-bit word in
  *         two's complement.[get]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  buff   Buffer that stores data read
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_angular_rate_raw_get(stmdev_ctx_t *ctx, int16_t *val)
{
  uint8_t buff[6];
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_OUTX_L_G, buff, 6);
  val[0] = (int16_t)buff[1];
  val[0] = (val[0] * 256) + (int16_t)buff[0];
  val[1] = (int16_t)buff[3];
  val[1] = (val[1] * 256) + (int16_t)buff[2];
  val[2] = (int16_t)buff[5];
  val[2] = (val[2] * 256) + (int16_t)buff[4];

  return ret;
}

/**
  * @brief  Linear acceleration output register. The value is expressed as a
  *         16-bit word in two's complement.[get]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  buff   Buffer that stores data read
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_acceleration_raw_get(stmdev_ctx_t *ctx, int16_t *val)
{
  uint8_t buff[6];
  int32_t ret;

  ret = read_reg(ctx, LSM6DSR_OUTX_L_A, buff, 6);
  val[0] = (int16_t)buff[1];
  val[0] = (val[0] * 256) + (int16_t)buff[0];
  val[1] = (int16_t)buff[3];
  val[1] = (val[1] * 256) + (int16_t)buff[2];
  val[2] = (int16_t)buff[5];
  val[2] = (val[2] * 256) + (int16_t)buff[4];

  return ret;
}

/**
  * @brief  Get the calibration value for gyroscope ODR.[get]
  *
  * @param  ctx    Read / write interface definitions.(ptr)
  * @param  buff   Buffer that stores data read
  * @retval        Interface status (MANDATORY: return 0 -> no Error).
  *
  */
int32_t lsm6dsr_odr_cal_reg_get(stmdev_ctx_t *ctx, int8_t *val)
{
  uint8_t reg;
  int32_t ret;

  ret = read_reg(ctx, 0x1E, &reg, 1);  // Internal calibration register
  *val = (int8_t)reg;

  return ret;
}

/**
  * @brief  Convert raw gyroscope data to mdps (milli-degrees per second)
  *
  * @param  lsb    Raw gyroscope data
  * @retval        Converted value in mdps
  *
  */
float lsm6dsr_from_fs2000dps_to_mdps(int16_t lsb)
{
  return ((float)lsb * 70.0f);
}

/**
  * @brief  Convert raw accelerometer data to mg (milli-g)
  *
  * @param  lsb    Raw accelerometer data
  * @retval        Converted value in mg
  *
  */
float lsm6dsr_from_fs2g_to_mg(int16_t lsb)
{
  return ((float)lsb * 0.061f);
}
