//
// Created by f<PERSON><PERSON><PERSON> on 2024/7/23.
//

#include "key.h"

uint8_t get_key_num()
{
    uint8_t keyNum = 0;
    if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_24) == 0){
        delay_ms(20);
        while (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_24) == 0);
        delay_ms(20);
        keyNum = 2;
    }
    else if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_20) == 0){
        delay_ms(20);
        while (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_20) == 0);
        delay_ms(20);
        keyNum = 4;
    }
    else if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_19) == 0){
        delay_ms(20);
        while (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_19) == 0);
        delay_ms(20);
        keyNum = 3;
    }
    else if (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_18) == 0){
        delay_ms(20);
        while (DL_GPIO_readPins(GPIOB, DL_GPIO_PIN_18) == 0);
        delay_ms(20);
        keyNum = 1;
    }
    return keyNum;
}
