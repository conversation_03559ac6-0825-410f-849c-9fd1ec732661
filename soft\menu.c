//
// Created by faz<PERSON><PERSON> on 2023/12/26.
//

#include "menu.h"

xPage pageBuffer[MAX_PAGE_NUM];
uint8_t pageBufferPointer = 0;

xpPage add_page(char * name, xpPage parentPage, uint8_t(*func)(uint8_t))
{
    if (pageBufferPointer >= MAX_PAGE_NUM)
        return NULL;
    xpPage subPage = pageBuffer+pageBufferPointer;
    pageBufferPointer ++;

    subPage->name = name;
    subPage->parentPage = parentPage;
    subPage->func = func;

    if (parentPage == NULL)
    {
        subPage->lastPage = NULL;
        subPage->nextPage = NULL;
    }
    else
    {
        if (parentPage->subPageHead == NULL)
        {
            subPage->lastPage = NULL;
            parentPage->subPageHead = subPage;
            parentPage->subPageTail = subPage;
            parentPage->selectSubPage = subPage;
//            parentPage->show.showStartPage = subPage;
        }
        else
        {
            subPage->lastPage = parentPage->subPageTail;
            parentPage->subPageTail->nextPage = subPage;
            parentPage->subPageTail = subPage;
        }
    }
    return subPage;
}

xpPage nowPage;
uint8_t menu_move(uint8_t keyNum)
{
    if (nowPage->func != NULL)
        keyNum = nowPage->func(keyNum);

    uint8_t moveDir = NOP;
//    if (keyNum & ENCODER_POS_PREFIX){
//        keyNum &= 0x0f;
//        if (keyNum > 1)
//            keyNum = UP;
//        else
//            keyNum = NOP;
//    }
//    else if (keyNum & ENCODER_NEG_PREFIX){
//        keyNum &= 0x0f;
//        if (keyNum > 1)
//            keyNum = DOWN;
//        else
//            keyNum = NOP;
//    }

    if (keyNum == DOWN)
    {
        if (nowPage->selectSubPage->nextPage != NULL)
        {
            nowPage->selectSubPage = nowPage->selectSubPage->nextPage;
            moveDir = DOWN;
        }
    }
    else if (keyNum == UP)
    {
        if (nowPage->selectSubPage->lastPage != NULL)
        {
            nowPage->selectSubPage = nowPage->selectSubPage->lastPage;
            moveDir = UP;
        }
    }
    else if (keyNum == ENTER)
    {
        if (nowPage->selectSubPage->subPageHead != NULL || nowPage->selectSubPage->func != NULL)
        {
            nowPage = nowPage->selectSubPage;
            moveDir = ENTER;
        }

    }
    else if (keyNum == BACK)
    {
        if (nowPage->parentPage != NULL)
        {
            nowPage = nowPage->parentPage;
            nowPage->selectSubPage = nowPage->subPageHead;
            moveDir = BACK;
        }
    }
    else if (keyNum == NO_SHOW)
    {
        moveDir = NO_SHOW;
    }

    return moveDir;
}

struct {
    uint8_t framePos;
    xpPage showStartPage;
}show;
#if ANIMATION_FLAG
static uint8_t animationFlag = 0;
static uint16_t frameY = 0, targetFrameY = 0;
static float frameLen = 0, targetFrameLen = 0;
static int stepY = 0;
static float stepLen = 0;
#endif
void menu_show(uint8_t moveDir)
{
#if ANIMATION_FLAG
    if (moveDir != NO_SHOW){
        // ���ǲ���ʾ���߿ղ���ʱ��ִ��Ĭ����ʾ
        if (moveDir != NO_SHOW && (moveDir != NOP || animationFlag == 1)){
            // �����ǰ������
            // oled_clear_buffer();
            // ���������DOWN
            if (moveDir == DOWN){
                // �����ǰѡ�е��Ǳ�ҳ�����һ��
                // ��Ҫ���й���
                if (show.framePos >= MAX_SHOW_NUM-1){
                    // ���㱾ҳ��ʾ�ĵ�һ����滹�ж�����
                    xpPage pPage = show.showStartPage;
                    uint8_t i = 0;
                    while (pPage != NULL){
                        pPage = pPage->nextPage;
                        i ++;
                    }
                    // ������ڵ���ÿҳ��ʾ������������Ը��£�Ҫ��Ȼ�Ͳ���������
                    if (i >= MAX_SHOW_NUM-1) {
                        show.showStartPage = show.showStartPage->nextPage;
                    }
                }
                else{
                    // ���ǵĻ���ֱ���»�����
                    show.framePos ++;
                    targetFrameY = (show.framePos)*(FONT_HEIGHT+4);
                    stepY = ANIMATION_SPEED;
                }
                targetFrameLen = (float )strlen(nowPage->selectSubPage->name)*FONT_WIDTH+2+ROUND_RECT_R;
                stepLen = (float )(targetFrameLen-frameLen)/(FONT_HEIGHT+4)*(ANIMATION_SPEED);
                animationFlag = 1;
            }
            // ���������UP
            else if (moveDir == UP){
                // �����ǰѡ�е��Ǳ�ҳ��һ��
                // ��Ҫ����
                if (show.framePos == 0){
                    // ֻҪ��һ�����һ��ǿյģ��Ϳ��Թ���
                    if (show.showStartPage->lastPage != NULL) {
                        show.showStartPage = show.showStartPage->lastPage;
                    }
                }
                else{
                    // �����ֱ���ϻ�
                    show.framePos --;
                    targetFrameY = (show.framePos)*(FONT_HEIGHT+4);
                    stepY = -ANIMATION_SPEED;
                }
                targetFrameLen = (float )strlen(nowPage->selectSubPage->name)*FONT_WIDTH+2+ROUND_RECT_R;
                stepLen = (float )(targetFrameLen-frameLen)/(FONT_HEIGHT+4)*(ANIMATION_SPEED);
                animationFlag = 1;
            }
            else if (moveDir == ENTER || moveDir == BACK){
                if (nowPage->subPageHead != NULL) {
                    show.showStartPage = nowPage->subPageHead;
                    targetFrameLen = (float )strlen(nowPage->selectSubPage->name)*FONT_WIDTH+2+ROUND_RECT_R;
                    targetFrameY = 0;
                    stepLen = (float )(targetFrameLen-frameLen)/(FONT_HEIGHT+4)*(ANIMATION_SPEED);
                    animationFlag = 1;
                    stepY = -ANIMATION_SPEED;
//                    if (nowPage->parentPage != NULL)
                    show.framePos = 0;
                }
                else {
                    show.showStartPage = NULL;
                }
            }
            if (nowPage->selectSubPage != NULL){
//                uint8_t frameLen = strlen(nowPage->selectSubPage->name)*FONT_WIDTH+2+ROUND_RECT_R;
                xpPage showSubPage = show.showStartPage;
                uint8_t i = 0;
                while (i < MAX_SHOW_NUM){
                    // oled_show_string(DISPLAY_OFFSET_X+2, i * (FONT_HEIGHT+4) + 1 + DISPLAY_OFFSET_Y, showSubPage->name);
                    if (showSubPage->nextPage == NULL)
                        break;
                    showSubPage = showSubPage->nextPage;
                    i ++;
                }
                if (targetFrameY != frameY) {
                    frameY += stepY;
                    frameLen += stepLen;
                }
                else {
                    frameLen = targetFrameLen;
                    animationFlag = 0;
                }
                // oled_draw_fill_round_rect_xor(DISPLAY_OFFSET_X, frameY, frameLen, FONT_HEIGHT+4, ROUND_RECT_R);
                // oled_update_screen();
            }
        }
    }
#else
    // ���ǲ���ʾ���߿ղ���ʱ��ִ��Ĭ����ʾ
    if (moveDir != NO_SHOW && moveDir != NOP){
        // �����ǰ������
        // oled_clear_buffer();
        // ���������DOWN
        if (moveDir == DOWN){
            // �����ǰѡ�е��Ǳ�ҳ�����һ��
            // ��Ҫ���й���
            if (show.framePos >= MAX_SHOW_NUM-1){
                // ���㱾ҳ��ʾ�ĵ�һ����滹�ж�����
                xpPage pPage = show.showStartPage;
                uint8_t i = 0;
                while (pPage != NULL){
                    pPage = pPage->nextPage;
                    i ++;
                }
                // ������ڵ���ÿҳ��ʾ������������Ը��£�Ҫ��Ȼ�Ͳ���������
                if (i >= MAX_SHOW_NUM-1) {
                    show.showStartPage = show.showStartPage->nextPage;
                }
            }
            else{
                // ���ǵĻ���ֱ���»�����
                show.framePos ++;
            }
        }
        // ���������UP
        else if (moveDir == UP){
            // �����ǰѡ�е��Ǳ�ҳ��һ��
            // ��Ҫ����
            if (show.framePos == 0){
                // ֻҪ��һ�����һ��ǿյģ��Ϳ��Թ���
                if (show.showStartPage->lastPage != NULL) {
                    show.showStartPage = show.showStartPage->lastPage;
                }
            }
            else{
                // �����ֱ���ϻ�
                show.framePos --;
            }
        }
        else if (moveDir == ENTER || moveDir == BACK){
            show.showStartPage = nowPage->subPageHead;
            if (nowPage->parentPage != NULL)
                show.framePos = 0;
        }
        if (nowPage->selectSubPage != NULL){
            uint8_t frameLen = strlen(nowPage->selectSubPage->name)*FONT_WIDTH+2+ROUND_RECT_R;
            xpPage showSubPage = show.showStartPage;
            uint8_t i = 0;
            while (i < MAX_SHOW_NUM){
                // oled_show_string(DISPLAY_OFFSET_X+2, i * (FONT_HEIGHT+4) + 1 + DISPLAY_OFFSET_Y, showSubPage->name);
                if (showSubPage->nextPage == NULL)
                    break;
                showSubPage = showSubPage->nextPage;
                i ++;
            }
            // oled_draw_fill_round_rect_xor(DISPLAY_OFFSET_X, (show.framePos)*(FONT_HEIGHT+4)+DISPLAY_OFFSET_Y, frameLen, FONT_HEIGHT+4, ROUND_RECT_R);
            // oled_update_screen();
        }
    }
#endif
}


void menu_task()
{
    uint8_t keyNum;
    keyNum = get_key_num();
    menu_show(menu_move(keyNum));
}

void menu_init()
{
    xpPage MainPage = add_page("nudec menu", NULL, NULL);
    add_page("show attitude", MainPage, show_attitude);
    add_page("remote control", MainPage, remote_control);
 /*---------------------�������޸�----------------------------*/
    nowPage = MainPage;
    show.showStartPage = MainPage->subPageHead;
#if ANIMATION_FLAG
    frameLen = targetFrameLen = strlen(nowPage->selectSubPage->name)*FONT_WIDTH+ROUND_RECT_R;
    frameY = targetFrameY = 0;
#endif
    menu_show(ONLY_SHOW);
}
