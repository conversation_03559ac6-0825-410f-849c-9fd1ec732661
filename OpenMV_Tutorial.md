# OpenMV与MSPM0G3507通信教程

## 📡 通信协议详解

### 数据帧格式
```
[0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59]
```

| 字节位置 | 内容 | 说明 |
|---------|------|------|
| 0 | 0x2B | 帧头1 |
| 1 | 0x11 | 帧头2 |
| 2 | data0 | 检测标志 (0=未检测到, 1=检测到目标) |
| 3 | data1 | X轴偏移量 (有符号，-128到127) |
| 4 | data2 | 距离低字节 (0-255) |
| 5 | data3 | 距离高字节 (0-255) |
| 6 | 0x5A | 帧尾1 |
| 7 | 0x59 | 帧尾2 |

### 数据解析
- **距离计算**: `distance = data2 + (data3 << 8)` (单位：毫米)
- **偏移量**: data1为有符号数，负值表示目标在左侧，正值表示在右侧

## 🔧 硬件连接

### UART连接
```
OpenMV ←→ MSPM0G3507
P4 (TX) ←→ PA9 (UART1_RX)
P5 (RX) ←→ PA8 (UART1_TX)
GND     ←→ GND
VCC     ←→ 3.3V
```

### 引脚配置确认
- MSPM0G3507侧：UART1，波特率9600
- OpenMV侧：UART1，波特率9600

## 💻 代码使用说明

### 1. OpenMV代码修改要点

#### 原始代码问题：
- 使用UART2，与MSPM0配置不匹配
- 波特率115200，与MSPM0的9600不匹配
- 数据帧格式不一致

#### 修改后的代码特点：
- 使用UART1，匹配MSPM0的UART1配置
- 波特率改为9600
- 数据帧格式完全匹配接收端

### 2. MSPM0G3507代码功能

#### 数据接收流程：
1. UART3中断接收数据
2. 验证帧头 (0x2B, 0x11)
3. 提取数据字段
4. 验证帧尾 (0x5A, 0x59)
5. 解析并存储数据

#### 数据显示功能：
- 实时显示检测状态
- 显示X轴偏移量
- 显示目标距离
- 显示方向指示
- 超时检测和错误处理

## 🖥️ TFT屏幕显示内容

### 正常检测到目标时：
```
OpenMV Data:
Target: FOUND
X Offset: -25
Distance: 1250 mm
Direction: LEFT
```

### 未检测到目标时：
```
OpenMV Data:
Target: NOT FOUND
X Offset: ---
Distance: ---
Direction: ---
```

### 通信异常时：
```
OpenMV Data:
Status: NO DATA
Check Connection
```

## 🔍 调试方法

### 1. 串口调试
在OpenMV代码中启用调试输出：
```python
print(f"Sent: flag={detection_flag}, x={offset_x}, dist={int(distance_mm)}")
```

### 2. 硬件检查
- 确认UART连接正确
- 检查波特率设置
- 验证电源供应

### 3. 软件调试
- 检查UART中断是否正常触发
- 验证数据帧格式
- 确认超时机制工作正常

## ⚙️ 参数调整

### OpenMV参数：
- `red_threshold`: 红色检测阈值
- `KNOWN_WIDTH`: 目标实际宽度(mm)
- `FOCAL_LENGTH_PIXELS`: 相机焦距(像素)
- 发送频率: 20Hz (50ms间隔)

### MSPM0参数：
- `OPENMV_DATA_TIMEOUT_MS`: 数据超时时间(500ms)
- 显示刷新频率: 10Hz (100ms间隔)

## 🚀 使用步骤

1. **硬件连接**: 按照连接图连接OpenMV和MSPM0
2. **烧录代码**: 
   - 将修改后的Python代码烧录到OpenMV
   - 编译并烧录MSPM0代码
3. **启动系统**: 
   - 先启动MSPM0
   - 再启动OpenMV
4. **观察显示**: 在TFT屏幕上观察数据显示
5. **调试优化**: 根据实际效果调整参数

## 📝 注意事项

1. **波特率匹配**: 确保两端波特率一致
2. **数据格式**: 严格按照协议格式发送数据
3. **超时处理**: 实现数据超时检测机制
4. **错误恢复**: 添加通信错误恢复机制
5. **性能优化**: 合理设置发送和显示频率

## 🔧 故障排除

### 问题1: 屏幕显示"NO DATA"
- 检查UART连接
- 确认波特率设置
- 验证OpenMV是否正常发送数据

### 问题2: 数据显示异常
- 检查数据帧格式
- 验证字节序
- 确认数据范围限制

### 问题3: 检测不稳定
- 调整红色阈值
- 优化光照条件
- 增加连续检测帧数要求
