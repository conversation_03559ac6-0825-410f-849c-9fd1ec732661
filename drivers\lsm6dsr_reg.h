/*
 * LSM6DSR Register Definitions
 * Based on STMicroelectronics LSM6DSR driver
 */

#ifndef LSM6DSR_REGS_H
#define LSM6DSR_REGS_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stddef.h>
#include <math.h>

/** I2C Device Address 8 bit format  if SA0=0 -> D5 if SA0=1 -> D7 **/
#define LSM6DSR_I2C_ADD_L                    0xD5U
#define LSM6DSR_I2C_ADD_H                    0xD7U

/** Device Identification (Who am I) **/
#define LSM6DSR_ID                           0x6BU

typedef int32_t (*stmdev_write_ptr)(void *, uint8_t, const uint8_t *, uint16_t);
typedef int32_t (*stmdev_read_ptr)(void *, uint8_t, uint8_t *, uint16_t);
typedef void (*stmdev_mdelay_ptr)(uint32_t millisec);

typedef struct
{
  /** Component mandatory fields **/
  stmdev_write_ptr  write_reg;
  stmdev_read_ptr   read_reg;
  stmdev_mdelay_ptr mdelay;
  /** Component optional fields **/
  void *handle;
} stmdev_ctx_t;

/**
  * @defgroup    LSM6DSR_Infos
  * @brief       This section groups all the functions concerning device info
  * @{
  *
  */

typedef struct
{
  uint8_t bit0       : 1;
  uint8_t bit1       : 1;
  uint8_t bit2       : 1;
  uint8_t bit3       : 1;
  uint8_t bit4       : 1;
  uint8_t bit5       : 1;
  uint8_t bit6       : 1;
  uint8_t bit7       : 1;
} bitwise_t;

#define PROPERTY_DISABLE                (0U)
#define PROPERTY_ENABLE                 (1U)

/**
  * @defgroup  LSM6DSR_Register_Union
  * @brief     This union group all the registers having a bit-field
  *            description.
  *            This union is useful but it's not needed by the driver.
  *
  *            REMOVING this union you are compliant with:
  *            MISRA-C 2012 [Rule 19.2] -> " Union are not allowed "
  *
  * @{
  *
  */

typedef union
{
  int16_t i16bit[3];
  uint8_t u8bit[6];
} axis3bit16_t;

typedef union
{
  int16_t i16bit;
  uint8_t u8bit[2];
} axis1bit16_t;

typedef union
{
  int32_t i32bit[3];
  uint8_t u8bit[12];
} axis3bit32_t;

typedef union
{
  int32_t i32bit;
  uint8_t u8bit[4];
} axis1bit32_t;

/**
  * @defgroup LSM6DSR_Register_Map
  * @brief    This section contains all the functions prototypes
  *           concerning the registers map
  * @{
  *
  */

#define LSM6DSR_FUNC_CFG_ACCESS              0x01U
#define LSM6DSR_PIN_CTRL                     0x02U
#define LSM6DSR_S4S_TPH_L                    0x04U
#define LSM6DSR_S4S_TPH_H                    0x05U
#define LSM6DSR_S4S_RR                       0x06U
#define LSM6DSR_FIFO_CTRL1                   0x07U
#define LSM6DSR_FIFO_CTRL2                   0x08U
#define LSM6DSR_FIFO_CTRL3                   0x09U
#define LSM6DSR_FIFO_CTRL4                   0x0AU
#define LSM6DSR_COUNTER_BDR_REG1             0x0BU
#define LSM6DSR_COUNTER_BDR_REG2             0x0CU
#define LSM6DSR_INT1_CTRL                    0x0DU
#define LSM6DSR_INT2_CTRL                    0x0EU
#define LSM6DSR_WHO_AM_I                     0x0FU
#define LSM6DSR_CTRL1_XL                     0x10U
#define LSM6DSR_CTRL2_G                      0x11U
#define LSM6DSR_CTRL3_C                      0x12U
#define LSM6DSR_CTRL4_C                      0x13U
#define LSM6DSR_CTRL5_C                      0x14U
#define LSM6DSR_CTRL6_C                      0x15U
#define LSM6DSR_CTRL7_G                      0x16U
#define LSM6DSR_CTRL8_XL                     0x17U
#define LSM6DSR_CTRL9_XL                     0x18U
#define LSM6DSR_CTRL10_C                     0x19U
#define LSM6DSR_ALL_INT_SRC                  0x1AU
#define LSM6DSR_WAKE_UP_SRC                  0x1BU
#define LSM6DSR_TAP_SRC                      0x1CU
#define LSM6DSR_D6D_SRC                      0x1DU
#define LSM6DSR_STATUS_REG                   0x1EU
#define LSM6DSR_OUT_TEMP_L                   0x20U
#define LSM6DSR_OUT_TEMP_H                   0x21U
#define LSM6DSR_OUTX_L_G                     0x22U
#define LSM6DSR_OUTX_H_G                     0x23U
#define LSM6DSR_OUTY_L_G                     0x24U
#define LSM6DSR_OUTY_H_G                     0x25U
#define LSM6DSR_OUTZ_L_G                     0x26U
#define LSM6DSR_OUTZ_H_G                     0x27U
#define LSM6DSR_OUTX_L_A                     0x28U
#define LSM6DSR_OUTX_H_A                     0x29U
#define LSM6DSR_OUTY_L_A                     0x2AU
#define LSM6DSR_OUTY_H_A                     0x2BU
#define LSM6DSR_OUTZ_L_A                     0x2CU
#define LSM6DSR_OUTZ_H_A                     0x2DU

/**
  * @defgroup LSM6DSR_data_rate
  * @brief    This section groups all the data rate enumerations
  * @{
  *
  */

typedef enum
{
  LSM6DSR_XL_ODR_OFF    = 0,
  LSM6DSR_XL_ODR_12Hz5  = 1,
  LSM6DSR_XL_ODR_26Hz   = 2,
  LSM6DSR_XL_ODR_52Hz   = 3,
  LSM6DSR_XL_ODR_104Hz  = 4,
  LSM6DSR_XL_ODR_208Hz  = 5,
  LSM6DSR_XL_ODR_416Hz  = 6,
  LSM6DSR_XL_ODR_833Hz  = 7,
  LSM6DSR_XL_ODR_1667Hz = 8,
  LSM6DSR_XL_ODR_3333Hz = 9,
  LSM6DSR_XL_ODR_6667Hz = 10,
} lsm6dsr_odr_xl_t;

typedef enum
{
  LSM6DSR_GY_ODR_OFF    = 0,
  LSM6DSR_GY_ODR_12Hz5  = 1,
  LSM6DSR_GY_ODR_26Hz   = 2,
  LSM6DSR_GY_ODR_52Hz   = 3,
  LSM6DSR_GY_ODR_104Hz  = 4,
  LSM6DSR_GY_ODR_208Hz  = 5,
  LSM6DSR_GY_ODR_416Hz  = 6,
  LSM6DSR_GY_ODR_833Hz  = 7,
  LSM6DSR_GY_ODR_1667Hz = 8,
  LSM6DSR_GY_ODR_3333Hz = 9,
  LSM6DSR_GY_ODR_6667Hz = 10,
} lsm6dsr_odr_g_t;

/**
  * @defgroup LSM6DSR_full_scale
  * @brief    This section groups all the full scale enumerations
  * @{
  *
  */

typedef enum
{
  LSM6DSR_2g   = 0,
  LSM6DSR_16g  = 1,
  LSM6DSR_4g   = 2,
  LSM6DSR_8g   = 3,
} lsm6dsr_fs_xl_t;

typedef enum
{
  LSM6DSR_250dps   = 0,
  LSM6DSR_500dps   = 1,
  LSM6DSR_1000dps  = 2,
  LSM6DSR_2000dps  = 3,
} lsm6dsr_fs_g_t;

typedef enum
{
  LSM6DSR_I3C_DISABLE         = 0,
  LSM6DSR_I3C_ENABLE_T_50us   = 1,
  LSM6DSR_I3C_ENABLE_T_2us    = 2,
  LSM6DSR_I3C_ENABLE_T_1ms    = 3,
} lsm6dsr_i3c_disable_t;

typedef enum
{
  LSM6DSR_DRDY_LATCHED = 0,
  LSM6DSR_DRDY_PULSED  = 1,
} lsm6dsr_dataready_pulsed_t;

typedef struct
{
  uint8_t int1_drdy_xl             : 1;
  uint8_t int1_drdy_g              : 1;
  uint8_t int1_boot                : 1;
  uint8_t int1_fth                 : 1;
  uint8_t int1_fifo_ovr            : 1;
  uint8_t int1_full_flag           : 1;
  uint8_t int1_cnt_bdr             : 1;
  uint8_t den_drdy_flag            : 1;
} lsm6dsr_int1_ctrl_t;

typedef struct
{
  lsm6dsr_int1_ctrl_t  int1_ctrl;
} lsm6dsr_pin_int1_route_t;

// Function prototypes
int32_t lsm6dsr_device_id_get(stmdev_ctx_t *ctx, uint8_t *buff);
int32_t lsm6dsr_reset_set(stmdev_ctx_t *ctx, uint8_t val);
int32_t lsm6dsr_reset_get(stmdev_ctx_t *ctx, uint8_t *val);
int32_t lsm6dsr_i3c_disable_set(stmdev_ctx_t *ctx, lsm6dsr_i3c_disable_t val);
int32_t lsm6dsr_block_data_update_set(stmdev_ctx_t *ctx, uint8_t val);
int32_t lsm6dsr_xl_data_rate_set(stmdev_ctx_t *ctx, lsm6dsr_odr_xl_t val);
int32_t lsm6dsr_gy_data_rate_set(stmdev_ctx_t *ctx, lsm6dsr_odr_g_t val);
int32_t lsm6dsr_xl_full_scale_set(stmdev_ctx_t *ctx, lsm6dsr_fs_xl_t val);
int32_t lsm6dsr_gy_full_scale_set(stmdev_ctx_t *ctx, lsm6dsr_fs_g_t val);
int32_t lsm6dsr_gy_filter_lp1_set(stmdev_ctx_t *ctx, uint8_t val);
int32_t lsm6dsr_pin_int1_route_get(stmdev_ctx_t *ctx, lsm6dsr_pin_int1_route_t *val);
int32_t lsm6dsr_pin_int1_route_set(stmdev_ctx_t *ctx, lsm6dsr_pin_int1_route_t *val);
int32_t lsm6dsr_data_ready_mode_set(stmdev_ctx_t *ctx, lsm6dsr_dataready_pulsed_t val);
int32_t lsm6dsr_odr_cal_reg_get(stmdev_ctx_t *ctx, int8_t *val);
int32_t lsm6dsr_gy_flag_data_ready_get(stmdev_ctx_t *ctx, uint8_t *val);
int32_t lsm6dsr_angular_rate_raw_get(stmdev_ctx_t *ctx, int16_t *val);
int32_t lsm6dsr_acceleration_raw_get(stmdev_ctx_t *ctx, int16_t *val);

// Conversion functions
float lsm6dsr_from_fs2000dps_to_mdps(int16_t lsb);
float lsm6dsr_from_fs2g_to_mg(int16_t lsb);

#ifdef __cplusplus
}
#endif

#endif /* LSM6DSR_REGS_H */
