/*
 * Copyright (c) 2023, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "common_inc.h"

incremental_pid_t left_speed_pid;
incremental_pid_t right_speed_pid;

int16_t target_speed;

// IMU测试相关变量
static uint32_t imu_update_counter = 0;

// TFT180屏幕测试函数
void tft180_test_basic(void)
{
    // 测试1: 清屏测试 - 显示不同颜色背景
    tft180_clear_color(RED);
    delay_ms(1000);

    tft180_clear_color(GREEN);
    delay_ms(1000);

    tft180_clear_color(BLUE);
    delay_ms(1000);

    tft180_clear_color(WHITE);
    delay_ms(1000);
}

void tft180_test_text(void)
{
    // 测试2: 字符串显示测试
    tft180_clear_color(BLACK);
    tft180_show_string_color(10, 10, "TFT180 Test", BLACK, WHITE);
    tft180_show_string_color(10, 30, "Hello World!", BLACK, YELLOW);
    tft180_show_string_color(10, 50, "MSPM0G3507", BLACK, GREEN);
    tft180_show_string_color(10, 70, "Screen OK!", BLACK, CYAN);
    delay_ms(3000);
}

void tft180_test_numbers(void)
{
    // 测试3: 数字显示测试
    tft180_clear_color(BLUE);
    tft180_show_string_color(10, 10, "Number Test:", BLUE, WHITE);

    // 显示整数
    tft180_show_string_color(10, 30, "Int:", BLUE, WHITE);
    tft180_show_int(50, 30, 12345, 5);

    // 显示浮点数
    tft180_show_string_color(10, 50, "Float:", BLUE, WHITE);
    tft180_show_float(60, 50, 123.456, 3, 3);

    tft180_show_string_color(10, 70, "Neg:", BLUE, WHITE);
    tft180_show_float(50, 70, -98.76, 2, 2);
    delay_ms(3000);
}

void tft180_test_colors(void)
{
    // 测试4: 颜色测试
    tft180_clear_color(BLACK);
    tft180_show_string_color(10, 10, "Color Test:", BLACK, WHITE);
    tft180_show_string_color(10, 30, "RED", BLACK, RED);
    tft180_show_string_color(60, 30, "GREEN", BLACK, GREEN);
    tft180_show_string_color(10, 50, "BLUE", BLACK, BLUE);
    tft180_show_string_color(60, 50, "YELLOW", BLACK, YELLOW);
    tft180_show_string_color(10, 70, "CYAN", BLACK, CYAN);
    tft180_show_string_color(60, 70, "PURPLE", BLACK, PURPLE);
    delay_ms(3000);
}

// 简单的IMU数据显示函数
void display_imu_data(void)
{
    tft180_show_num_color(40, 25, acceleration_mg[0], 4, 1, BLACK, GREEN);

    tft180_show_num_color(40, 40, acceleration_mg[1], 4, 1, BLACK, GREEN);

    tft180_show_num_color(40, 55, acceleration_mg[2], 4, 1, BLACK, GREEN);

    tft180_show_num_color(40, 75, angular_rate_mdps[0]/1000.0f, 4, 1, BLACK, CYAN);

    tft180_show_num_color(40, 90, angular_rate_mdps[1]/1000.0f, 4, 1, BLACK, CYAN);

    tft180_show_num_color(40, 105, angular_rate_mdps[2]/1000.0f, 4, 1, BLACK, CYAN);
}

int main(void)
{
    SYSCFG_DL_init();

    // 初始化TFT180屏幕
    tft180_init();
    delay_ms(500);

    // 初始化IMU660RB
    tft180_clear_color(BLACK);
    // IMU660RB_Init();

    // // 执行陀螺仪零偏校准
    // tft180_show_string_color(10, 10, "Gyro Calibrating...", BLACK, YELLOW);
    // gyroOffsetInit();
    // tft180_show_string_color(10, 10, "Gyro Calibrated!   ", BLACK, GREEN);
    delay_ms(500);
    tft180_clear_color(BLACK);

    // 初始化OpenMV通信
    openmv_init();
    tft180_show_string_color(10, 10, "OpenMV Initialized!", BLACK, GREEN);
    // delay_ms(500);
    // tft180_clear_color(BLACK);

    // 初始化定时器中断
    timerA_init();  // 5ms定时器
    timerB_init();  // 15ms定时器
    
    // 添加一个计数器用于定期检查通信状态
    uint32_t comm_check_counter = 0;

    while (1) {
        // 显示OpenMV数据
        openmv_display_data();

        // 每1000次循环检查一次通信状态（约10秒）
        comm_check_counter++;
        if (comm_check_counter >= 10) {
            comm_check_counter = 0;
            // 如果长时间没有收到有效数据，重置UART状态
            if (!openmv_is_data_valid()) {
                openmv_reset_uart_state();
                tft180_show_string_color(10, 90, "UART Reset", BLACK, YELLOW);
                // delay_ms(100);
            }
        }


        // 显示欧拉角和IMU数据（在屏幕下半部分）
        // imu660rb_euler_show();
    }
}

// 5ms
void timerA_callback()
{
    
    // encoder_callback();
    // int16_t left_duty = (int16_t)incremental_pid(&left_speed_pid, left_speed, target_speed);
    // int16_t right_duty = (int16_t)incremental_pid(&right_speed_pid, right_speed, target_speed);
    // motor_set_duty(left_duty, right_duty);
//    vofa_add_data(left_speed);
//    vofa_add_data(right_speed);
//    vofa_add_data(target_speed);
//    vofa_send();
}

// 15ms
void timerB_callback()
{
//    printf("timerB\n");
}

void GROUP1_IRQHandler(void)
{
    encoder_exti_callback();
}


