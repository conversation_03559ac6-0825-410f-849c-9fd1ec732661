import sensor, image, time, math 
from machine import UART

# ---------------------------
# 传感器初始化设置
# ---------------------------
sensor.reset()                             # 初始化摄像头
sensor.set_pixformat(sensor.RGB565)          # 设置为彩色模式
sensor.set_framesize(sensor.QQVGA)           # 分辨率设置为 QQVGA (160x120)
sensor.set_vflip(True)                     # 垂直翻转
sensor.set_hmirror(True)                   # 水平镜像（要实现180度效果需要同时垂直和水平翻转）
sensor.skip_frames(time=2000)                # 等待设置稳定
sensor.set_auto_gain(False)                  # 关闭自动增益
sensor.set_auto_whitebal(False)              # 关闭自动白平衡
sensor.set_brightness(300)  # 根据需要调整亮度

clock = time.clock()                         # 用于帧率统计

# ---------------------------
# 初始化 UART1，用于向 MSPM0G3507 发送数据
# 注意：这里改为UART1，对应你的配置中的PA8/PA9
# ---------------------------
uart = UART(1, baudrate=9600)  # 修改为9600波特率，与MSPM0配置匹配

# ---------------------------
# 红色阈值设置（LAB色彩空间）
# ---------------------------
red_threshold = (20, 70, 19, 127, -2, 50)

# ---------------------------
# 测距相关参数（请根据实际情况校准）
# ---------------------------
KNOWN_WIDTH = 30.0         # 物体实际宽度（单位：毫米）
FOCAL_LENGTH_PIXELS = 200  # 焦距（单位：像素），需通过相机标定获得

# ---------------------------
# 连续检测变量初始化
# ---------------------------
detected_count = 0           # 连续检测到符合条件的帧数
prev_blob_center = None      # 上一帧红色区域的中心点
CENTER_OFFSET_THRESHOLD = 20 # 判断同一区域允许的中心点偏差（单位：像素）

def send_data_to_mspm0(detection_flag, offset_x, distance_mm):
    """
    发送数据到MSPM0G3507
    数据格式：[0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59]
    data0: 检测标志 (0=未检测到, 1=检测到)
    data1: X轴偏移量 (有符号，-128到127)
    data2: 距离低字节
    data3: 距离高字节
    """
    # 限制偏移量范围
    if offset_x > 127:
        offset_x = 127
    elif offset_x < -128:
        offset_x = -128
    
    # 限制距离范围（0-65535mm）
    if distance_mm > 65535:
        distance_mm = 65535
    elif distance_mm < 0:
        distance_mm = 0
    
    # 转换为无符号字节
    data0 = detection_flag & 0xFF
    data1 = offset_x & 0xFF  # 有符号转无符号
    data2 = int(distance_mm) & 0xFF        # 距离低字节
    data3 = (int(distance_mm) >> 8) & 0xFF # 距离高字节
    
    # 构造消息
    msg = bytearray([0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59])
    uart.write(msg)
    
    # 调试输出
    print(f"Sent: flag={detection_flag}, x={offset_x}, dist={int(distance_mm)}")

while True:
    clock.tick()
    img = sensor.snapshot()                # 获取当前帧图像
    
    # ---------------------------
    # 红色区域检测
    # ---------------------------
    blobs = img.find_blobs([red_threshold],
                           roi=(0, 0, 160, sensor.height()),
                           pixels_threshold=200,
                           area_threshold=60,
                           merge=True)

    # ---------------------------
    # 绘制图像中心
    # ---------------------------
    cx_img = sensor.width() // 2
    cy_img = sensor.height() // 2
    img.draw_circle(cx_img, cy_img, 3, color=(0, 0, 255))  # 蓝色圆点表示图像中心

    # ---------------------------
    # 对检测到的红色区域进行处理
    # ---------------------------
    valid_blob = None  # 保存本帧中满足条件的红色区域
    for blob in blobs:
        # 绘制检测框（调试用）
        img.draw_rectangle(blob.rect(), color=(0, 255, 0))
        
        # 计算偏移量
        offset_x = blob.cx() - cx_img
        offset_y = blob.cy() - cy_img
        
        # 判断该区域是否满足像素点要求
        if blob.pixels() > 50:
            if valid_blob is None or blob.pixels() > valid_blob.pixels():
                valid_blob = blob

    # ---------------------------
    # 连续检测判断（连续5帧检测到同一目标）
    # ---------------------------
    if valid_blob:
        if prev_blob_center is None:
            prev_blob_center = (valid_blob.cx(), valid_blob.cy())
            detected_count = 1
        else:
            dx = valid_blob.cx() - prev_blob_center[0]
            dy = valid_blob.cy() - prev_blob_center[1]
            # 使用平方比较，避免调用 math.sqrt
            if dx * dx + dy * dy < CENTER_OFFSET_THRESHOLD * CENTER_OFFSET_THRESHOLD:
                detected_count += 1
            else:
                detected_count = 1
            prev_blob_center = (valid_blob.cx(), valid_blob.cy())
    else:
        detected_count = 0
        prev_blob_center = None

    # ---------------------------
    # 数据发送
    # ---------------------------
    if detected_count >= 5 and valid_blob is not None:
        # 检测到目标
        offset_x = valid_blob.cx() - cx_img
        distance_mm = (KNOWN_WIDTH * FOCAL_LENGTH_PIXELS) / valid_blob.w()
        
        # 在图像上显示信息
        img.draw_string(10, 30, "Dist: %d mm" % int(distance_mm), color=(0, 255, 0), scale=2)
        img.draw_string(10, 50, "X: %d" % offset_x, color=(0, 255, 0), scale=2)
        
        # 发送数据
        send_data_to_mspm0(1, offset_x, distance_mm)
    else:
        # 未检测到目标
        img.draw_string(10, 30, "No Target", color=(255, 0, 0), scale=2)
        send_data_to_mspm0(0, 0, 0)

    # 延时
    time.sleep_ms(50)  # 50ms发送一次，20Hz
