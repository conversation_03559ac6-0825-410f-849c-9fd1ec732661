# OpenMV简单测试程序
# 用于验证与MSPM0G3507的通信是否正常

import time
from machine import UART

# 初始化UART1，波特率9600
uart = UART(1, baudrate=9600)

def send_test_data(detection_flag, offset_x, distance_mm):
    """
    发送测试数据到MSPM0G3507
    """
    # 限制数据范围
    if offset_x > 127:
        offset_x = 127
    elif offset_x < -128:
        offset_x = -128
    
    if distance_mm > 65535:
        distance_mm = 65535
    elif distance_mm < 0:
        distance_mm = 0
    
    # 构造数据
    data0 = detection_flag & 0xFF
    data1 = offset_x & 0xFF
    data2 = int(distance_mm) & 0xFF
    data3 = (int(distance_mm) >> 8) & 0xFF
    
    # 发送数据帧
    msg = bytearray([0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59])
    uart.write(msg)
    
    print(f"Sent: flag={detection_flag}, x={offset_x}, dist={int(distance_mm)}")

# 测试序列
test_cases = [
    (0, 0, 0),        # 未检测到目标
    (1, 0, 1000),     # 检测到目标，居中，距离1000mm
    (1, 50, 800),     # 检测到目标，右偏50，距离800mm
    (1, -30, 1200),   # 检测到目标，左偏30，距离1200mm
    (1, 100, 500),    # 检测到目标，右偏100，距离500mm
    (0, 0, 0),        # 未检测到目标
]

print("OpenMV Test Program Started")
print("Sending test data to MSPM0G3507...")

test_index = 0
while True:
    # 循环发送测试数据
    detection_flag, offset_x, distance_mm = test_cases[test_index]
    send_test_data(detection_flag, offset_x, distance_mm)
    
    # 切换到下一个测试用例
    test_index = (test_index + 1) % len(test_cases)
    
    # 等待2秒
    time.sleep(2)
