/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3505

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)



#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_6 */
#define PWM_6_INST                                                         TIMG6
#define PWM_6_INST_IRQHandler                                   TIMG6_IRQHandler
#define PWM_6_INST_INT_IRQN                                     (TIMG6_INT_IRQn)
#define PWM_6_INST_CLK_FREQ                                              6400000
/* GPIO defines for channel 0 */
#define GPIO_PWM_6_C0_PORT                                                 GPIOB
#define GPIO_PWM_6_C0_PIN                                          DL_GPIO_PIN_2
#define GPIO_PWM_6_C0_IOMUX                                      (IOMUX_PINCM15)
#define GPIO_PWM_6_C0_IOMUX_FUNC                     IOMUX_PINCM15_PF_TIMG6_CCP0
#define GPIO_PWM_6_C0_IDX                                    DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_6_C1_PORT                                                 GPIOA
#define GPIO_PWM_6_C1_PIN                                         DL_GPIO_PIN_22
#define GPIO_PWM_6_C1_IOMUX                                      (IOMUX_PINCM47)
#define GPIO_PWM_6_C1_IOMUX_FUNC                     IOMUX_PINCM47_PF_TIMG6_CCP1
#define GPIO_PWM_6_C1_IDX                                    DL_TIMER_CC_1_INDEX

/* Defines for PWM_7 */
#define PWM_7_INST                                                         TIMG7
#define PWM_7_INST_IRQHandler                                   TIMG7_IRQHandler
#define PWM_7_INST_INT_IRQN                                     (TIMG7_INT_IRQn)
#define PWM_7_INST_CLK_FREQ                                              6400000
/* GPIO defines for channel 0 */
#define GPIO_PWM_7_C0_PORT                                                 GPIOA
#define GPIO_PWM_7_C0_PIN                                         DL_GPIO_PIN_28
#define GPIO_PWM_7_C0_IOMUX                                       (IOMUX_PINCM3)
#define GPIO_PWM_7_C0_IOMUX_FUNC                      IOMUX_PINCM3_PF_TIMG7_CCP0
#define GPIO_PWM_7_C0_IDX                                    DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_7_C1_PORT                                                 GPIOA
#define GPIO_PWM_7_C1_PIN                                         DL_GPIO_PIN_24
#define GPIO_PWM_7_C1_IOMUX                                      (IOMUX_PINCM54)
#define GPIO_PWM_7_C1_IOMUX_FUNC                     IOMUX_PINCM54_PF_TIMG7_CCP1
#define GPIO_PWM_7_C1_IDX                                    DL_TIMER_CC_1_INDEX



/* Defines for TIMER_8 */
#define TIMER_8_INST                                                     (TIMG8)
#define TIMER_8_INST_IRQHandler                                 TIMG8_IRQHandler
#define TIMER_8_INST_INT_IRQN                                   (TIMG8_INT_IRQn)
#define TIMER_8_INST_LOAD_VALUE                                           (499U)
/* Defines for TIMER_12 */
#define TIMER_12_INST                                                   (TIMG12)
#define TIMER_12_INST_IRQHandler                               TIMG12_IRQHandler
#define TIMER_12_INST_INT_IRQN                                 (TIMG12_INT_IRQn)
#define TIMER_12_INST_LOAD_VALUE                                        (59999U)



/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                            4000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                  (9600)
#define UART_0_IBRD_4_MHZ_9600_BAUD                                         (26)
#define UART_0_FBRD_4_MHZ_9600_BAUD                                          (3)
/* Defines for UART_1 */
#define UART_1_INST                                                        UART1
#define UART_1_INST_FREQUENCY                                           32000000
#define UART_1_INST_IRQHandler                                  UART1_IRQHandler
#define UART_1_INST_INT_IRQN                                      UART1_INT_IRQn
#define GPIO_UART_1_RX_PORT                                                GPIOA
#define GPIO_UART_1_TX_PORT                                                GPIOA
#define GPIO_UART_1_RX_PIN                                         DL_GPIO_PIN_9
#define GPIO_UART_1_TX_PIN                                         DL_GPIO_PIN_8
#define GPIO_UART_1_IOMUX_RX                                     (IOMUX_PINCM20)
#define GPIO_UART_1_IOMUX_TX                                     (IOMUX_PINCM19)
#define GPIO_UART_1_IOMUX_RX_FUNC                      IOMUX_PINCM20_PF_UART1_RX
#define GPIO_UART_1_IOMUX_TX_FUNC                      IOMUX_PINCM19_PF_UART1_TX
#define UART_1_BAUD_RATE                                                  (9600)
#define UART_1_IBRD_32_MHZ_9600_BAUD                                       (208)
#define UART_1_FBRD_32_MHZ_9600_BAUD                                        (21)
/* Defines for UART_3 */
#define UART_3_INST                                                        UART3
#define UART_3_INST_FREQUENCY                                           32000000
#define UART_3_INST_IRQHandler                                  UART3_IRQHandler
#define UART_3_INST_INT_IRQN                                      UART3_INT_IRQn
#define GPIO_UART_3_RX_PORT                                                GPIOA
#define GPIO_UART_3_TX_PORT                                                GPIOA
#define GPIO_UART_3_RX_PIN                                        DL_GPIO_PIN_13
#define GPIO_UART_3_TX_PIN                                        DL_GPIO_PIN_14
#define GPIO_UART_3_IOMUX_RX                                     (IOMUX_PINCM35)
#define GPIO_UART_3_IOMUX_TX                                     (IOMUX_PINCM36)
#define GPIO_UART_3_IOMUX_RX_FUNC                      IOMUX_PINCM35_PF_UART3_RX
#define GPIO_UART_3_IOMUX_TX_FUNC                      IOMUX_PINCM36_PF_UART3_TX
#define UART_3_BAUD_RATE                                                  (9600)
#define UART_3_IBRD_32_MHZ_9600_BAUD                                       (208)
#define UART_3_FBRD_32_MHZ_9600_BAUD                                        (21)




/* Defines for SPI_IMU660RB */
#define SPI_IMU660RB_INST                                                  SPI1
#define SPI_IMU660RB_INST_IRQHandler                            SPI1_IRQHandler
#define SPI_IMU660RB_INST_INT_IRQN                                SPI1_INT_IRQn
#define GPIO_SPI_IMU660RB_PICO_PORT                                       GPIOB
#define GPIO_SPI_IMU660RB_PICO_PIN                               DL_GPIO_PIN_15
#define GPIO_SPI_IMU660RB_IOMUX_PICO                            (IOMUX_PINCM32)
#define GPIO_SPI_IMU660RB_IOMUX_PICO_FUNC            IOMUX_PINCM32_PF_SPI1_PICO
#define GPIO_SPI_IMU660RB_POCI_PORT                                       GPIOB
#define GPIO_SPI_IMU660RB_POCI_PIN                               DL_GPIO_PIN_14
#define GPIO_SPI_IMU660RB_IOMUX_POCI                            (IOMUX_PINCM31)
#define GPIO_SPI_IMU660RB_IOMUX_POCI_FUNC            IOMUX_PINCM31_PF_SPI1_POCI
/* GPIO configuration for SPI_IMU660RB */
#define GPIO_SPI_IMU660RB_SCLK_PORT                                       GPIOB
#define GPIO_SPI_IMU660RB_SCLK_PIN                               DL_GPIO_PIN_16
#define GPIO_SPI_IMU660RB_IOMUX_SCLK                            (IOMUX_PINCM33)
#define GPIO_SPI_IMU660RB_IOMUX_SCLK_FUNC            IOMUX_PINCM33_PF_SPI1_SCLK
/* Defines for TFT_SPI */
#define TFT_SPI_INST                                                       SPI0
#define TFT_SPI_INST_IRQHandler                                 SPI0_IRQHandler
#define TFT_SPI_INST_INT_IRQN                                     SPI0_INT_IRQn
#define GPIO_TFT_SPI_PICO_PORT                                            GPIOB
#define GPIO_TFT_SPI_PICO_PIN                                    DL_GPIO_PIN_17
#define GPIO_TFT_SPI_IOMUX_PICO                                 (IOMUX_PINCM43)
#define GPIO_TFT_SPI_IOMUX_PICO_FUNC                 IOMUX_PINCM43_PF_SPI0_PICO
#define GPIO_TFT_SPI_POCI_PORT                                            GPIOA
#define GPIO_TFT_SPI_POCI_PIN                                     DL_GPIO_PIN_4
#define GPIO_TFT_SPI_IOMUX_POCI                                  (IOMUX_PINCM9)
#define GPIO_TFT_SPI_IOMUX_POCI_FUNC                  IOMUX_PINCM9_PF_SPI0_POCI
/* GPIO configuration for TFT_SPI */
#define GPIO_TFT_SPI_SCLK_PORT                                            GPIOA
#define GPIO_TFT_SPI_SCLK_PIN                                    DL_GPIO_PIN_12
#define GPIO_TFT_SPI_IOMUX_SCLK                                 (IOMUX_PINCM34)
#define GPIO_TFT_SPI_IOMUX_SCLK_FUNC                 IOMUX_PINCM34_PF_SPI0_SCLK
#define GPIO_TFT_SPI_CS0_PORT                                             GPIOA
#define GPIO_TFT_SPI_CS0_PIN                                      DL_GPIO_PIN_2
#define GPIO_TFT_SPI_IOMUX_CS0                                   (IOMUX_PINCM7)
#define GPIO_TFT_SPI_IOMUX_CS0_FUNC                    IOMUX_PINCM7_PF_SPI0_CS0



/* Port definition for Pin Group GPIOA */
#define GPIOA_PORT                                                       (GPIOA)

/* Defines for PIN_25: GPIOA.25 with pinCMx 55 on package pin 26 */
// pins affected by this interrupt request:["PIN_25","PIN_26","PIN_12"]
#define GPIOA_INT_IRQN                                          (GPIOA_INT_IRQn)
#define GPIOA_INT_IIDX                          (DL_INTERRUPT_GROUP1_IIDX_GPIOA)
#define GPIOA_PIN_25_IIDX                                   (DL_GPIO_IIDX_DIO25)
#define GPIOA_PIN_25_PIN                                        (DL_GPIO_PIN_25)
#define GPIOA_PIN_25_IOMUX                                       (IOMUX_PINCM55)
/* Defines for PIN_26: GPIOA.26 with pinCMx 59 on package pin 30 */
#define GPIOA_PIN_26_IIDX                                   (DL_GPIO_IIDX_DIO26)
#define GPIOA_PIN_26_PIN                                        (DL_GPIO_PIN_26)
#define GPIOA_PIN_26_IOMUX                                       (IOMUX_PINCM59)
/* Defines for PIN_12: GPIOA.15 with pinCMx 37 on package pin 8 */
#define GPIOA_PIN_12_IIDX                                   (DL_GPIO_IIDX_DIO15)
#define GPIOA_PIN_12_PIN                                        (DL_GPIO_PIN_15)
#define GPIOA_PIN_12_IOMUX                                       (IOMUX_PINCM37)
/* Port definition for Pin Group GPIOB */
#define GPIOB_PORT                                                       (GPIOB)

/* Defines for PIN_24: GPIOB.24 with pinCMx 52 on package pin 23 */
#define GPIOB_PIN_24_PIN                                        (DL_GPIO_PIN_24)
#define GPIOB_PIN_24_IOMUX                                       (IOMUX_PINCM52)
/* Defines for PIN_20: GPIOB.20 with pinCMx 48 on package pin 19 */
#define GPIOB_PIN_20_PIN                                        (DL_GPIO_PIN_20)
#define GPIOB_PIN_20_IOMUX                                       (IOMUX_PINCM48)
/* Defines for PIN_19: GPIOB.19 with pinCMx 45 on package pin 16 */
#define GPIOB_PIN_19_PIN                                        (DL_GPIO_PIN_19)
#define GPIOB_PIN_19_IOMUX                                       (IOMUX_PINCM45)
/* Defines for PIN_18: GPIOB.18 with pinCMx 44 on package pin 15 */
#define GPIOB_PIN_18_PIN                                        (DL_GPIO_PIN_18)
#define GPIOB_PIN_18_IOMUX                                       (IOMUX_PINCM44)
/* Defines for PIN_8: GPIOB.8 with pinCMx 25 on package pin 60 */
// groups represented: ["GPIO_IMU660RB","GPIOB"]
// pins affected: ["IMU660RB_INT1","PIN_8","PIN_9"]
#define GPIO_MULTIPLE_GPIOB_INT_IRQN                            (GPIOB_INT_IRQn)
#define GPIO_MULTIPLE_GPIOB_INT_IIDX            (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define GPIOB_PIN_8_IIDX                                     (DL_GPIO_IIDX_DIO8)
#define GPIOB_PIN_8_PIN                                          (DL_GPIO_PIN_8)
#define GPIOB_PIN_8_IOMUX                                        (IOMUX_PINCM25)
/* Defines for PIN_9: GPIOB.9 with pinCMx 26 on package pin 61 */
#define GPIOB_PIN_9_IIDX                                     (DL_GPIO_IIDX_DIO9)
#define GPIOB_PIN_9_PIN                                          (DL_GPIO_PIN_9)
#define GPIOB_PIN_9_IOMUX                                        (IOMUX_PINCM26)
/* Port definition for Pin Group TFT_PORT */
#define TFT_PORT_PORT                                                    (GPIOB)

/* Defines for TFT_DC: GPIOB.10 with pinCMx 27 on package pin 62 */
#define TFT_PORT_TFT_DC_PIN                                     (DL_GPIO_PIN_10)
#define TFT_PORT_TFT_DC_IOMUX                                    (IOMUX_PINCM27)
/* Defines for TFT_RES: GPIOB.11 with pinCMx 28 on package pin 63 */
#define TFT_PORT_TFT_RES_PIN                                    (DL_GPIO_PIN_11)
#define TFT_PORT_TFT_RES_IOMUX                                   (IOMUX_PINCM28)
/* Defines for TFT_CS: GPIOB.12 with pinCMx 29 on package pin 64 */
#define TFT_PORT_TFT_CS_PIN                                     (DL_GPIO_PIN_12)
#define TFT_PORT_TFT_CS_IOMUX                                    (IOMUX_PINCM29)
/* Defines for TFT_BL: GPIOB.7 with pinCMx 24 on package pin 59 */
#define TFT_PORT_TFT_BL_PIN                                      (DL_GPIO_PIN_7)
#define TFT_PORT_TFT_BL_IOMUX                                    (IOMUX_PINCM24)
/* Port definition for Pin Group GPIO_IMU660RB */
#define GPIO_IMU660RB_PORT                                               (GPIOB)

/* Defines for IMU660RB_CS: GPIOB.13 with pinCMx 30 on package pin 1 */
#define GPIO_IMU660RB_IMU660RB_CS_PIN                           (DL_GPIO_PIN_13)
#define GPIO_IMU660RB_IMU660RB_CS_IOMUX                          (IOMUX_PINCM30)
/* Defines for IMU660RB_INT1: GPIOB.6 with pinCMx 23 on package pin 58 */
#define GPIO_IMU660RB_IMU660RB_INT1_IIDX                     (DL_GPIO_IIDX_DIO6)
#define GPIO_IMU660RB_IMU660RB_INT1_PIN                          (DL_GPIO_PIN_6)
#define GPIO_IMU660RB_IMU660RB_INT1_IOMUX                        (IOMUX_PINCM23)



/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_6_init(void);
void SYSCFG_DL_PWM_7_init(void);
void SYSCFG_DL_TIMER_8_init(void);
void SYSCFG_DL_TIMER_12_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_UART_1_init(void);
void SYSCFG_DL_UART_3_init(void);
void SYSCFG_DL_SPI_IMU660RB_init(void);
void SYSCFG_DL_TFT_SPI_init(void);

void SYSCFG_DL_SYSTICK_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
