//
// Created by faz<PERSON><PERSON> on 2024/7/27.
//

#ifndef FLASH_H
#define FLASH_H

#include "common_inc.h"

#define W25Q32_SPI_CS_H()   DL_GPIO_setPins(GPIOB, DL_GPIO_PIN_17)
#define W25Q32_SPI_CS_L()   DL_GPIO_clearPins(GPIOB, DL_GPIO_PIN_17)

typedef union {
    float float_type;
    uint32_t uint32_type;
    int32_t int32_type;
    uint16_t uint16_type;
    int16_t int16_type;
    uint8_t uint8_type;
    int8_t int8_type;
}flash_data_union;

extern flash_data_union flash_union_buffer[4096/4];

//uint16_t w25q32_readID();
//void w25q32_write(uint8_t sector_num, uint8_t * buffer, uint16_t len);
//void w25q32_read(uint8_t sector_num, uint8_t * buffer, uint16_t len);
uint8_t flash_init();
void flash_write_from_buffer(uint8_t sector_num, uint16_t len);
void flash_read_to_buffer(uint8_t sector_num, uint16_t len);
void flash_clear_buffer();
void flash_write_parameter();
void flash_read_parameter();

#endif //FLASH_H
