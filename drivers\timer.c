//
// Created by f<PERSON><PERSON><PERSON> on 2024/7/24.
//

#include "timer.h"

// 系统时间计数器（毫秒）
static volatile uint32_t system_time_ms = 0;

void timerA_init()
{
    NVIC_ClearPendingIRQ(TIMG8_INT_IRQn);
    NVIC_EnableIRQ(TIMG8_INT_IRQn);
}

void timerB_init()
{
    NVIC_ClearPendingIRQ(TIMG12_INT_IRQn);
    NVIC_EnableIRQ(TIMG12_INT_IRQn);
}

void TIMG8_IRQHandler(void)
{
    switch( DL_TimerG_getPendingInterrupt(TIMG8) )
    {
        case DL_TIMER_IIDX_ZERO://如果是0溢出中断
            // 更新系统时间（5ms定时器）
            system_time_ms += 5;
            timerA_callback();
            break;
        default://其他的定时器中断
            break;
    }
}

void TIMG12_IRQHandler(void)
{
    switch( DL_TimerG_getPendingInterrupt(TIMG12) )
    {
        case DL_TIMER_IIDX_ZERO://如果是0溢出中断
            timerB_callback();
            break;
        default://其他的定时器中断
            break;
    }
}

// 获取系统时间（毫秒）
uint32_t get_system_time_ms(void)
{
    return system_time_ms;
}
