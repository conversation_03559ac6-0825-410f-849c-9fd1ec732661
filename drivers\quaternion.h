#ifndef _QUATERNION_H_
#define _QUATERNION_H_

#include <math.h>

#define DELTA_T     0.005f   // 采样周期5ms 即频率200Hz (对应您的5ms定时器中断)

// 欧拉角输出变量
extern float eulerAngle_yaw;
extern float eulerAngle_pitch;
extern float eulerAngle_roll;

// 陀螺仪偏移量
extern float gx_offset, gy_offset, gz_offset;

// 函数声明
void gyroOffsetInit(void);              // 陀螺仪偏移量初始化
float invSqrt(float x);                 // 快速平方根倒数
void icmGetValues(void);                // 数据滤波和单位转换
void icmAHRSupdate(void);               // 四元数姿态解算
void imu660rb_euler_show(void);         // 显示姿态角度 (保持原函数名兼容)

#endif // _QUATERNION_H_
