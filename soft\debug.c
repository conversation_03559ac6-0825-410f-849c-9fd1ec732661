//
// Created by faz<PERSON><PERSON> on 2024/7/22.
//

#include "debug.h"

volatile unsigned char uart_data = 0;

void debug_init()
{
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
}

void debug_send_char(char ch)
{
    //当串口0忙的时候等待，不忙的时候再发送传进来的字符
    while(DL_UART_isBusy(UART_0_INST) == true);
    //发送单个字符
    DL_UART_Main_transmitData(UART_0_INST, ch);
}

void debug_send_buffer(uint8_t * buffer, uint8_t len)
{
    for (int i = 0; i < len; ++i) {
        debug_send_char(buffer[i]);
    }
}

void debug_send_string(char * str)
{
    while (*str != 0 && str != 0){
        debug_send_char(*str++);
    }
}

//串口的中断服务函数
void UART_0_INST_IRQHandler(void)
{
    //如果产生了串口中断
    switch( DL_UART_getPendingInterrupt(UART_0_INST) )
    {
        case DL_UART_IIDX_RX://如果是接收中断
            //接发送过来的数据保存在变量中
            uart_data = DL_UART_Main_receiveData(UART_0_INST);
            //将保存的数据再发送出去
//            uart0_send_char(uart_data);
            break;

        default://其他的串口中断
            break;
    }
}

int fputc(int ch, FILE *stream)
{
    while( DL_UART_isBusy(UART_0_INST) == true );
    DL_UART_Main_transmitData(UART_0_INST, ch);
    return ch;
}

#if !defined(__MICROLIB)
//不使用微库的话就需要添加下面的函数
#if (__ARMCLIB_VERSION <= 6000000)
//如果编译器是AC5  就定义下面这个结构体
struct __FILE
{
    int handle;
};
#endif
FILE __stdout;
//定义_sys_exit()以避免使用半主机模式
void _sys_exit(int x)
{
    x = x;
}
#endif
